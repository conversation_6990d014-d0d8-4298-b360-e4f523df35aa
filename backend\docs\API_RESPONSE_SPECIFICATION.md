# API响应规范说明

## 概述

本文档说明了团队管理系统后端API的统一响应规范，确保所有API响应都遵循一致的格式。

## 核心原则

### 1. HTTP状态码统一返回200

- **所有API响应的HTTP状态码都统一返回200 OK**
- 无论是成功还是异常情况，HTTP层面都返回200
- 这样可以避免前端因HTTP状态码导致的网络层异常处理

### 2. 业务状态码在响应体中处理

真正的成功/失败状态通过ApiResponse中的`code`字段来表示：

- **成功**：`code = 200`
- **业务异常**：`code = 400, 404, 403` 等错误状态码
- **系统异常**：`code = 500` 内部服务器错误

### 3. 统一响应格式

所有API响应都使用相同的JSON结构：

```json
{
  "code": 200,
  "message": "success",
  "data": {...},
  "timestamp": "2024-01-01 12:00:00"
}
```

## 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | Integer | 业务状态码，200表示成功，其他表示各种错误 |
| message | String | 响应消息，成功时为"success"，失败时为具体错误信息 |
| data | Object | 响应数据，成功时包含实际数据，失败时通常为null |
| timestamp | String | 响应时间戳，格式为"yyyy-MM-dd HH:mm:ss" |

## 标准化响应代码定义

### 成功状态码
- `200`: 操作成功
  - 用于所有成功的API操作
  - 响应消息通常为"success"或具体的成功描述

### 客户端错误状态码

#### 400 - 请求参数错误
- **使用场景**: 请求参数格式错误、必填参数缺失、参数值不合法等
- **示例**:
  - "用户名不能为空"
  - "邮箱格式不正确"
  - "密码长度至少8位"

#### 401 - 未认证
- **使用场景**: 用户未登录或Token无效/过期
- **前端处理**: 自动跳转到登录页面，清除本地认证信息
- **示例**:
  - "登录已过期，请重新登录"
  - "Token无效"

#### 403 - 权限不足
- **使用场景**: 用户已认证但无权限访问特定资源
- **前端处理**: 显示权限不足提示，不跳转页面
- **示例**:
  - "没有权限访问该资源"
  - "您不是该团队的成员"
  - "团队已被停用"

#### 404 - 资源不存在
- **使用场景**: 请求的资源不存在
- **示例**:
  - "用户不存在"
  - "团队不存在"
  - "订阅计划不存在"

#### 409 - 资源冲突
- **使用场景**: 资源重复创建、数据冲突等
- **示例**:
  - "邮箱已被注册"
  - "团队名称已存在"
  - "用户已在该团队中"

#### 422 - 请求语义错误
- **使用场景**: 请求格式正确但业务逻辑验证失败
- **示例**:
  - "验证码错误"
  - "密码不符合安全要求"
  - "操作不被允许"

#### 429 - 请求频率限制
- **使用场景**: 用户请求频率超过限制
- **前端处理**: 显示频率限制提示，建议用户稍后重试
- **示例**:
  - "请求频率过高，请60秒后重试"
  - "验证码发送过于频繁"

### 服务器错误状态码

#### 500 - 服务器内部错误
- **使用场景**: 系统内部错误、未捕获的异常
- **前端处理**: 显示通用错误提示，建议用户稍后重试
- **示例**:
  - "系统内部错误，请联系管理员"
  - "服务暂时不可用"

#### 502 - 网关错误
- **使用场景**: 网关或代理服务器错误
- **示例**: "网关错误，请稍后重试"

#### 503 - 服务不可用
- **使用场景**: 服务暂时不可用、维护中
- **示例**:
  - "服务暂时不可用，请稍后重试"
  - "系统维护中"

#### 504 - 网关超时
- **使用场景**: 网关或代理服务器超时
- **示例**: "请求超时，请稍后重试"

## 异常处理机制

### 全局异常处理器

`GlobalExceptionHandler` 负责捕获所有异常并转换为统一的响应格式：

1. **业务异常** (`BusinessException`): 返回自定义错误码和消息
2. **资源不存在异常** (`ResourceNotFoundException`): 返回404状态码
3. **权限不足异常** (`InsufficientPermissionException`): 返回403状态码
4. **认证异常** (`AuthenticationException`): 返回401状态码
5. **团队访问被拒绝异常** (`TeamAccessDeniedException`): 返回403状态码
6. **参数验证异常**: 返回400状态码
7. **系统异常**: 返回500状态码

### Spring Security异常处理

- **认证入口点** (`JwtAuthenticationEntryPoint`): 处理未认证请求
- **访问拒绝处理器** (`JwtAccessDeniedHandler`): 处理权限不足请求

## 标准化错误处理机制

### 后端实现

#### 1. 响应代码常量类
```java
// ResponseCode.java - 统一的响应代码定义
public final class ResponseCode {
    public static final int SUCCESS = 200;
    public static final int BAD_REQUEST = 400;
    public static final int UNAUTHORIZED = 401;
    public static final int FORBIDDEN = 403;
    public static final int NOT_FOUND = 404;
    // ... 其他代码
}
```

#### 2. 标准化异常类
```java
// BusinessException.java - 业务异常
public class BusinessException extends RuntimeException {
    private final Integer code;

    public BusinessException(String message) {
        super(message);
        this.code = ResponseCode.BAD_REQUEST;
    }

    public static BusinessException unauthorized(String message) {
        return new BusinessException(ResponseCode.UNAUTHORIZED, message);
    }
    // ... 其他工厂方法
}
```

#### 3. 全局异常处理器
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public ApiResponse<Object> handleBusinessException(BusinessException e) {
        return ApiResponse.error(e.getCode(), e.getMessage());
    }
}
```

### 前端实现

#### 1. 响应代码常量
```typescript
// responseCodes.ts - 与后端保持一致
export const ResponseCode = {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    // ... 其他代码
} as const;
```

#### 2. 集中式错误处理器
```typescript
// errorHandler.ts - 统一错误处理
export const handleApiError = (response: ApiResponse<any>) => {
    if (ResponseCode.isSuccess(response.code)) return;

    switch (response.code) {
        case ResponseCode.UNAUTHORIZED:
            // 跳转到登录页
            break;
        case ResponseCode.FORBIDDEN:
            message.error(response.message);
            break;
        default:
            message.error(response.message);
    }
};
```

#### 3. 请求拦截器集成
```typescript
// requestErrorConfig.ts - 全局错误处理
export const errorConfig: RequestConfig = {
    errorConfig: {
        errorHandler: (error: any) => {
            if (error.name === 'ApiError') {
                const apiResponse = {
                    code: error.info.errorCode,
                    message: error.info.errorMessage,
                    data: error.info.data,
                };
                handleApiError(apiResponse);
            }
        }
    }
};
```

## 优势

1. **简化前端处理**：前端不需要处理复杂的HTTP状态码
2. **统一错误处理**：所有错误都通过相同的机制处理
3. **更好的调试体验**：错误信息更加明确和一致
4. **避免网络层异常**：减少因HTTP状态码导致的意外错误

## 注意事项

1. 所有Controller方法都应该返回`ApiResponse<T>`类型
2. Service层抛出的异常会被全局异常处理器自动捕获和转换
3. 自定义异常应该继承相应的基础异常类
4. 错误消息应该对用户友好，避免暴露系统内部信息
