/**
 * 错误处理工具测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import { message, notification } from 'antd';
import { history } from '@umijs/max';
import {
  handleApiError,
  ErrorDisplayType,
  showSuccess,
  showWarning,
  showError,
  showInfo,
  showNotification,
  createErrorHandler,
} from '../errorHandler';
import { ResponseCode } from '@/constants/responseCodes';
import type { ApiResponse } from '@/types/api';

// Mock antd components
jest.mock('antd', () => ({
  message: {
    success: jest.fn(),
    warning: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
  notification: {
    success: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock history
jest.mock('@umijs/max', () => ({
  history: {
    push: jest.fn(),
  },
}));

// Mock AuthService
jest.mock('@/services', () => ({
  AuthService: {
    clearTokens: jest.fn(),
  },
}));

describe('ErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/test',
      },
      writable: true,
    });
  });

  describe('handleApiError', () => {
    test('should not handle success response', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.SUCCESS,
        message: 'success',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.error).not.toHaveBeenCalled();
      expect(message.warning).not.toHaveBeenCalled();
    });

    test('should handle bad request error', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.error).toHaveBeenCalledWith('参数错误');
    });

    test('should handle unauthorized error with redirect', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.UNAUTHORIZED,
        message: '登录已过期',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.error).toHaveBeenCalledWith('登录已过期');
      expect(history.push).toHaveBeenCalledWith('/user/login');
    });

    test('should handle forbidden error', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.FORBIDDEN,
        message: '权限不足',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.error).toHaveBeenCalledWith('权限不足');
    });

    test('should handle conflict error with warning', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.CONFLICT,
        message: '资源冲突',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.warning).toHaveBeenCalledWith('资源冲突');
    });

    test('should handle too many requests with warning', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.TOO_MANY_REQUESTS,
        message: '请求频率过高',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.warning).toHaveBeenCalledWith('请求频率过高');
    });

    test('should handle server error with custom message', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.INTERNAL_SERVER_ERROR,
        message: '数据库连接失败',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response);

      expect(message.error).toHaveBeenCalledWith('服务器内部错误，请稍后重试');
    });

    test('should use custom config', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      const config = {
        displayType: ErrorDisplayType.NOTIFICATION,
        customMessage: '自定义错误消息',
      };

      handleApiError(response, config);

      expect(notification.error).toHaveBeenCalledWith({
        message: '操作失败',
        description: '自定义错误消息',
        duration: 4.5,
      });
    });

    test('should call error callback', () => {
      const onError = jest.fn();
      const response: ApiResponse<any> = {
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response, { onError });

      expect(onError).toHaveBeenCalledWith({
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        response,
      });
    });

    test('should handle silent error', () => {
      const response: ApiResponse<any> = {
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      handleApiError(response, { displayType: ErrorDisplayType.SILENT });

      expect(message.error).not.toHaveBeenCalled();
      expect(message.warning).not.toHaveBeenCalled();
      expect(notification.error).not.toHaveBeenCalled();
    });
  });

  describe('Convenience Methods', () => {
    test('showSuccess should call message.success', () => {
      showSuccess('操作成功');
      expect(message.success).toHaveBeenCalledWith('操作成功');
    });

    test('showWarning should call message.warning', () => {
      showWarning('警告消息');
      expect(message.warning).toHaveBeenCalledWith('警告消息');
    });

    test('showError should call message.error', () => {
      showError('错误消息');
      expect(message.error).toHaveBeenCalledWith('错误消息');
    });

    test('showInfo should call message.info', () => {
      showInfo('信息消息');
      expect(message.info).toHaveBeenCalledWith('信息消息');
    });

    test('showNotification should call notification with correct type', () => {
      showNotification('标题', '描述', 'success');
      expect(notification.success).toHaveBeenCalledWith({
        message: '标题',
        description: '描述',
        duration: 4.5,
      });
    });
  });

  describe('createErrorHandler', () => {
    test('should create error handler with default config', () => {
      const defaultConfig = { displayType: ErrorDisplayType.WARNING };
      const errorHandler = createErrorHandler(defaultConfig);

      const response: ApiResponse<any> = {
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      errorHandler(response);

      expect(message.warning).toHaveBeenCalledWith('参数错误');
    });

    test('should merge configs correctly', () => {
      const defaultConfig = { displayType: ErrorDisplayType.WARNING };
      const errorHandler = createErrorHandler(defaultConfig);

      const response: ApiResponse<any> = {
        code: ResponseCode.BAD_REQUEST,
        message: '参数错误',
        data: null,
        timestamp: '2024-01-01 12:00:00',
      };

      errorHandler(response, { displayType: ErrorDisplayType.ERROR });

      expect(message.error).toHaveBeenCalledWith('参数错误');
    });
  });
});
