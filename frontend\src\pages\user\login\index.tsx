/**
 * 登录页面
 * 实现双阶段认证的第一阶段：账号登录
 */

import { MailOutlined, SafetyOutlined } from '@ant-design/icons';
import { Helmet, history, useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Space,
  Typography,
} from 'antd';
import { createStyles } from 'antd-style';
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Footer } from '@/components';
import { AuthService } from '@/services';
import type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';
import Settings from '../../../../config/defaultSettings';

const { Title, Text } = Typography;

// 登录表单组件（移到外部避免重新创建）
const LoginFormComponent: React.FC<{
  form: any;
  handleLogin: (values: LoginRequest) => void;
  handleSendCode: () => void;
  sendingCode: boolean;
  countdown: number;
  loading: boolean;
}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {
  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染
  const sendCodeButton = useMemo(() => (
    <Button
      type="link"
      size="small"
      disabled={countdown > 0 || sendingCode}
      loading={sendingCode}
      onClick={handleSendCode}
      style={{ padding: 0, height: 'auto' }}
    >
      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}
    </Button>
  ), [countdown, sendingCode, handleSendCode]);

  // 使用 useMemo 稳定邮箱输入框，避免重新渲染
  const emailField = useMemo(() => (
    <Form.Item
      key="email-field"
      name="email"
      rules={[
        { required: true, message: '请输入邮箱！' },
        { type: 'email', message: '请输入有效的邮箱地址！' },
      ]}
    >
      <Input
        key="email-input"
        prefix={<MailOutlined />}
        placeholder="邮箱"
        autoComplete="email"
      />
    </Form.Item>
  ), []);

  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染
  const codeField = useMemo(() => (
    <Form.Item
      key="code-field"
      name="code"
      rules={[
        { required: true, message: '请输入验证码！' },
        { len: 6, message: '验证码为6位数字！' },
        { pattern: /^\d{6}$/, message: '验证码只能包含数字！' },
      ]}
    >
      <Input
        key="code-input"
        prefix={<SafetyOutlined />}
        placeholder="6位验证码"
        maxLength={6}
        suffix={sendCodeButton}
      />
    </Form.Item>
  ), [sendCodeButton]);

  return (
    <Form
      form={form}
      name="login"
      size="large"
      onFinish={handleLogin}
      autoComplete="off"
    >
      {emailField}
      {codeField}

      {/* 提示信息 */}
      <div style={{ marginBottom: 16, textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          新用户将自动完成注册并登录
        </Text>
      </div>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          登录 / 注册
        </Button>
      </Form.Item>
    </Form>
  );
});

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    logo: {
      marginBottom: 16,
    },
    title: {
      marginBottom: 0,
    },
    loginCard: {
      width: '100%',
      maxWidth: 400,
      boxShadow: token.boxShadowTertiary,
    },
    footer: {
      marginTop: 40,
      textAlign: 'center',
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      top: 16,
      borderRadius: token.borderRadius,
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [form] = Form.useForm(); // 将表单实例提升到父组件
  const { styles } = useStyles();
  const { setInitialState } = useModel('@@initialState');

  // 使用 Form 内置的邮箱验证

  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入
  useEffect(() => {
    setCountdown(0);
  }, []);

  // 倒计时效果
  React.useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  // 发送验证码
  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {
    let email: string;

    try {
      // 验证邮箱字段
      await form.validateFields(['email']);

      // 从表单获取邮箱值
      email = form.getFieldValue('email');
      console.log('发送验证码前的邮箱值:', email);

      if (!email) {
        message.error('请输入邮箱地址');
        return;
      }
    } catch (error) {
      // 表单验证失败
      message.error('请输入有效的邮箱地址');
      return;
    }

    setSendingCode(true);
    try {
      const request: SendVerificationCodeRequest = { email, type };
      const response = await AuthService.sendVerificationCode(request);

      if (response.success) {
        message.success(response.message);
        setCountdown(60); // 60秒倒计时

        // 验证码发送成功后检查表单值
        console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));

        // 在开发环境中提示查看控制台
        if (process.env.NODE_ENV === 'development') {
          message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);
        }
      } else {
        message.error(response.message);
        if (response.nextSendTime) {
          setCountdown(response.nextSendTime);
        }
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      message.error('发送验证码失败，请稍后重试');
    } finally {
      setSendingCode(false);
    }
  }, [form]);

  // 处理登录/注册
  const handleLogin = useCallback(async (values: LoginRequest) => {
    setLoading(true);
    try {
      const response = await AuthService.login(values);
      message.success('登录成功！');

      // 登录成功后停止倒计时
      setCountdown(0);

      // 登录成功后，刷新 initialState
      await setInitialState((prevState) => ({
        ...prevState,
        currentUser: response.user,
        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,
      }));

      // 等待一小段时间确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 根据团队数量进行不同的跳转处理
      if (response.teams.length === 0) {
        // 没有团队，跳转到个人中心页面
        history.push('/personal-center');
      } else {
        // 有团队（无论一个还是多个），都跳转到个人中心整合页面
        history.push('/personal-center', { teams: response.teams });
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  }, [setInitialState]);

  // 注册功能已移除，统一使用验证码登录/注册流程

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          登录 / 注册
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <div className={styles.logo}>
              <img src="/logo.svg" alt="TeamAuth" height={48} />
            </div>
            <div className={styles.title}>
              <Title level={2}>团队管理系统</Title>
              <Text type="secondary">现代化的团队协作与管理平台</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.loginCard}>
          <LoginFormComponent
            form={form}
            handleLogin={handleLogin}
            handleSendCode={() => handleSendCode('login')}
            sendingCode={sendingCode}
            countdown={countdown}
            loading={loading}
          />
        </Card>

        <div className={styles.footer}>
          <Text type="secondary">© 2025 TeamAuth. All rights reserved.</Text>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default LoginPage;
