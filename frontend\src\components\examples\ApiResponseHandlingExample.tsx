/**
 * API Response Handling Example Component
 * 
 * This component demonstrates the correct patterns for handling API responses
 * in the TeamAuth application. It shows how to:
 * 
 * 1. Use service layer methods that handle response.code checking
 * 2. Handle errors properly in components
 * 3. Display success and error messages consistently
 * 4. Use the response handler utilities
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Form,
  Input,
  message,
  Space,
  Typography,
  Spin,
  Alert,
} from 'antd';
import { UserService } from '@/services/user';
import { withSuccessMessage, withVoidResponse } from '@/utils/apiResponseHandler';
import type { UserProfileResponse, UpdateUserProfileRequest } from '@/types/api';

const { Title, Text } = Typography;

/**
 * Example component showing proper API response handling patterns
 */
const ApiResponseHandlingExample: React.FC = () => {
  const [userProfile, setUserProfile] = useState<UserProfileResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [form] = Form.useForm();

  // ============= Pattern 1: Basic Data Fetching =============
  
  /**
   * Fetch user data with proper error handling
   * The service layer handles response.code checking and error display
   */
  const fetchUserProfile = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Service method throws error if response.code !== 200
      // Error messages are automatically displayed by the service layer
      const profile = await UserService.getUserProfile();
      setUserProfile(profile);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      setError('获取用户资料失败，请稍后重试');
      // Error message already displayed by service layer
    } finally {
      setLoading(false);
    }
  };

  // ============= Pattern 2: Form Submission with Success Message =============
  
  /**
   * Update user profile with success message
   * Uses utility function to handle success/error messages
   */
  const handleUpdateProfile = async (values: UpdateUserProfileRequest) => {
    setLoading(true);
    
    try {
      // Using utility function for automatic success/error handling
      const updatedProfile = await withSuccessMessage(
        () => UserService.updateUserProfile(values),
        '用户资料更新成功',
        '用户资料更新失败'
      );
      
      if (updatedProfile) {
        setUserProfile(updatedProfile);
        form.resetFields();
      }
    } catch (error) {
      console.error('Profile update failed:', error);
      // Error already handled by utility function
    } finally {
      setLoading(false);
    }
  };

  // ============= Pattern 3: Void Operations =============
  
  /**
   * Delete user account (void operation)
   * Uses utility function for void responses
   */
  const handleDeleteAccount = async () => {
    const success = await withVoidResponse(
      () => UserService.deleteAccount('password'),
      '账户删除成功',
      '账户删除失败'
    );
    
    if (success) {
      // Handle successful deletion
      setUserProfile(null);
    }
  };

  // ============= Pattern 4: Manual Error Handling =============
  
  /**
   * Manual error handling for special cases
   * When you need custom logic for different error scenarios
   */
  const handleSpecialOperation = async () => {
    try {
      const result = await UserService.getUserPersonalStats();
      
      // Handle successful response
      console.log('Stats:', result);
      message.success('统计数据获取成功');
      
    } catch (error: any) {
      console.error('Special operation failed:', error);
      
      // Custom error handling based on error type
      if (error.message.includes('权限')) {
        message.warning('权限不足，请联系管理员');
      } else if (error.message.includes('网络')) {
        message.error('网络连接失败，请检查网络设置');
      } else {
        // Fallback error message
        message.error('操作失败，请稍后重试');
      }
    }
  };

  // ============= Pattern 5: Conditional Operations =============
  
  /**
   * Operations with conditions and multiple API calls
   */
  const handleConditionalOperation = async () => {
    try {
      // First, check if user has permission
      const hasPermission = await UserService.validateCurrentPassword('test');
      
      if (!hasPermission) {
        message.warning('密码验证失败，无法执行操作');
        return;
      }
      
      // If permission check passes, perform the main operation
      await UserService.updateUserName('新用户名');
      message.success('用户名更新成功');
      
      // Refresh data
      await fetchUserProfile();
      
    } catch (error) {
      console.error('Conditional operation failed:', error);
      // Errors already handled by individual service calls
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchUserProfile();
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>API Response Handling Examples</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* Data Display Section */}
        <Card title="用户资料" loading={loading}>
          {error && (
            <Alert
              message="加载失败"
              description={error}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
          
          {userProfile ? (
            <Space direction="vertical">
              <Text><strong>姓名:</strong> {userProfile.name}</Text>
              <Text><strong>邮箱:</strong> {userProfile.email}</Text>
              <Text><strong>电话:</strong> {userProfile.telephone || '未设置'}</Text>
              <Text><strong>职位:</strong> {userProfile.position || '未设置'}</Text>
            </Space>
          ) : (
            !loading && <Text type="secondary">暂无用户数据</Text>
          )}
        </Card>

        {/* Form Section */}
        <Card title="更新用户资料">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdateProfile}
            initialValues={userProfile || {}}
          >
            <Form.Item
              name="name"
              label="姓名"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input placeholder="请输入姓名" />
            </Form.Item>
            
            <Form.Item name="telephone" label="电话">
              <Input placeholder="请输入电话号码" />
            </Form.Item>
            
            <Form.Item name="position" label="职位">
              <Input placeholder="请输入职位" />
            </Form.Item>
            
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                更新资料
              </Button>
            </Form.Item>
          </Form>
        </Card>

        {/* Action Buttons Section */}
        <Card title="操作示例">
          <Space wrap>
            <Button onClick={fetchUserProfile} loading={loading}>
              刷新数据
            </Button>
            
            <Button onClick={handleSpecialOperation}>
              特殊操作示例
            </Button>
            
            <Button onClick={handleConditionalOperation}>
              条件操作示例
            </Button>
            
            <Button danger onClick={handleDeleteAccount}>
              删除账户示例
            </Button>
          </Space>
        </Card>

        {/* Documentation Section */}
        <Card title="代码说明">
          <Space direction="vertical">
            <Text>
              <strong>模式1 - 基础数据获取:</strong> 使用 try-catch 处理服务层调用，错误消息由服务层自动显示
            </Text>
            <Text>
              <strong>模式2 - 表单提交:</strong> 使用 withSuccessMessage 工具函数自动处理成功/错误消息
            </Text>
            <Text>
              <strong>模式3 - 无返回值操作:</strong> 使用 withVoidResponse 工具函数处理无数据返回的操作
            </Text>
            <Text>
              <strong>模式4 - 手动错误处理:</strong> 针对特殊场景的自定义错误处理逻辑
            </Text>
            <Text>
              <strong>模式5 - 条件操作:</strong> 多个API调用的组合操作，每个调用都有独立的错误处理
            </Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default ApiResponseHandlingExample;
