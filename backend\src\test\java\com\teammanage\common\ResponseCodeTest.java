package com.teammanage.common;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 响应代码常量测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class ResponseCodeTest {

    @Test
    void testSuccessCode() {
        assertEquals(200, ResponseCode.SUCCESS);
        assertTrue(ResponseCode.isSuccess(ResponseCode.SUCCESS));
        assertFalse(ResponseCode.isError(ResponseCode.SUCCESS));
    }

    @Test
    void testClientErrorCodes() {
        // 测试客户端错误代码
        assertTrue(ResponseCode.isClientError(ResponseCode.BAD_REQUEST));
        assertTrue(ResponseCode.isClientError(ResponseCode.UNAUTHORIZED));
        assertTrue(ResponseCode.isClientError(ResponseCode.FORBIDDEN));
        assertTrue(ResponseCode.isClientError(ResponseCode.NOT_FOUND));
        assertTrue(ResponseCode.isClientError(ResponseCode.CONFLICT));
        assertTrue(ResponseCode.isClientError(ResponseCode.UNPROCESSABLE_ENTITY));
        assertTrue(ResponseCode.isClientError(ResponseCode.TOO_MANY_REQUESTS));
        
        // 测试错误代码判断
        assertTrue(ResponseCode.isError(ResponseCode.BAD_REQUEST));
        assertTrue(ResponseCode.isError(ResponseCode.UNAUTHORIZED));
        assertTrue(ResponseCode.isError(ResponseCode.FORBIDDEN));
        assertTrue(ResponseCode.isError(ResponseCode.NOT_FOUND));
    }

    @Test
    void testServerErrorCodes() {
        // 测试服务器错误代码
        assertTrue(ResponseCode.isServerError(ResponseCode.INTERNAL_SERVER_ERROR));
        assertTrue(ResponseCode.isServerError(ResponseCode.BAD_GATEWAY));
        assertTrue(ResponseCode.isServerError(ResponseCode.SERVICE_UNAVAILABLE));
        assertTrue(ResponseCode.isServerError(ResponseCode.GATEWAY_TIMEOUT));
        
        // 测试错误代码判断
        assertTrue(ResponseCode.isError(ResponseCode.INTERNAL_SERVER_ERROR));
        assertTrue(ResponseCode.isError(ResponseCode.SERVICE_UNAVAILABLE));
    }

    @Test
    void testCodeDescriptions() {
        // 测试响应代码描述
        assertEquals("操作成功", ResponseCode.getDescription(ResponseCode.SUCCESS));
        assertEquals("请求参数错误", ResponseCode.getDescription(ResponseCode.BAD_REQUEST));
        assertEquals("未认证，需要登录", ResponseCode.getDescription(ResponseCode.UNAUTHORIZED));
        assertEquals("权限不足", ResponseCode.getDescription(ResponseCode.FORBIDDEN));
        assertEquals("资源不存在", ResponseCode.getDescription(ResponseCode.NOT_FOUND));
        assertEquals("资源冲突", ResponseCode.getDescription(ResponseCode.CONFLICT));
        assertEquals("请求语义错误", ResponseCode.getDescription(ResponseCode.UNPROCESSABLE_ENTITY));
        assertEquals("请求频率过高", ResponseCode.getDescription(ResponseCode.TOO_MANY_REQUESTS));
        assertEquals("服务器内部错误", ResponseCode.getDescription(ResponseCode.INTERNAL_SERVER_ERROR));
        assertEquals("网关错误", ResponseCode.getDescription(ResponseCode.BAD_GATEWAY));
        assertEquals("服务不可用", ResponseCode.getDescription(ResponseCode.SERVICE_UNAVAILABLE));
        assertEquals("网关超时", ResponseCode.getDescription(ResponseCode.GATEWAY_TIMEOUT));
        assertEquals("未知错误", ResponseCode.getDescription(999));
    }

    @Test
    void testCodeRanges() {
        // 测试代码范围判断
        assertFalse(ResponseCode.isClientError(ResponseCode.SUCCESS));
        assertFalse(ResponseCode.isServerError(ResponseCode.SUCCESS));
        
        assertFalse(ResponseCode.isClientError(ResponseCode.INTERNAL_SERVER_ERROR));
        assertFalse(ResponseCode.isServerError(ResponseCode.BAD_REQUEST));
        
        // 边界测试
        assertTrue(ResponseCode.isClientError(400));
        assertTrue(ResponseCode.isClientError(499));
        assertFalse(ResponseCode.isClientError(399));
        assertFalse(ResponseCode.isClientError(500));
        
        assertTrue(ResponseCode.isServerError(500));
        assertTrue(ResponseCode.isServerError(599));
        assertFalse(ResponseCode.isServerError(499));
        assertFalse(ResponseCode.isServerError(600));
    }

    @Test
    void testSpecificCodes() {
        // 测试具体的响应代码值
        assertEquals(200, ResponseCode.SUCCESS);
        assertEquals(400, ResponseCode.BAD_REQUEST);
        assertEquals(401, ResponseCode.UNAUTHORIZED);
        assertEquals(403, ResponseCode.FORBIDDEN);
        assertEquals(404, ResponseCode.NOT_FOUND);
        assertEquals(409, ResponseCode.CONFLICT);
        assertEquals(422, ResponseCode.UNPROCESSABLE_ENTITY);
        assertEquals(429, ResponseCode.TOO_MANY_REQUESTS);
        assertEquals(500, ResponseCode.INTERNAL_SERVER_ERROR);
        assertEquals(502, ResponseCode.BAD_GATEWAY);
        assertEquals(503, ResponseCode.SERVICE_UNAVAILABLE);
        assertEquals(504, ResponseCode.GATEWAY_TIMEOUT);
    }
}
