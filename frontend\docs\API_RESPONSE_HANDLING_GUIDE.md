# API Response Handling Guide

This guide explains how to consistently handle API responses in the TeamAuth frontend application, ensuring that `response.code` is always checked and errors are displayed using Ant Design's message component.

## Overview

The application uses a standardized API response format:

```typescript
interface ApiResponse<T> {
  code: number;      // HTTP-like status code (200 = success)
  message: string;   // Human-readable message
  data: T;          // Response data
  timestamp: string; // Response timestamp
}
```

**Key Principle**: Always check `response.code` before using `response.data`. If `code !== 200`, display the error message using Ant Design's message component.

## Response Handling Utilities

### 1. Basic Response Validation

```typescript
import { validateApiResponse, isSuccessResponse } from '@/utils/apiResponseHandler';

// Check if response is successful
if (isSuccessResponse(response)) {
  // Use response.data
}

// Validate and throw on error
validateApiResponse(response, 'Custom error message');
```

### 2. Service Layer Pattern

**Before (Incorrect)**:
```typescript
static async getUserProfile(): Promise<UserProfileResponse> {
  const response = await apiRequest.get<UserProfileResponse>('/users/profile');
  return response.data; // ❌ No code checking!
}
```

**After (Correct)**:
```typescript
static async getUserProfile(): Promise<UserProfileResponse> {
  const response = await apiRequest.get<UserProfileResponse>('/users/profile');
  
  // Check response code and handle errors
  if (!ResponseCode.isSuccess(response.code)) {
    handleApiError(response);
    throw new Error(response.message);
  }
  
  return response.data;
}
```

**Using Utilities (Recommended)**:
```typescript
import { extractDataOrThrow } from '@/utils/apiResponseHandler';

static async getUserProfile(): Promise<UserProfileResponse> {
  const response = await apiRequest.get<UserProfileResponse>('/users/profile');
  return extractDataOrThrow(response, '获取用户资料失败');
}
```

### 3. Component Usage Patterns

**Pattern 1: Basic Error Handling**
```typescript
const fetchData = async () => {
  try {
    const response = await UserService.getUserProfile();
    setUserData(response);
  } catch (error) {
    // Error already displayed by service layer
    console.error('Failed to fetch user data:', error);
  }
};
```

**Pattern 2: With Success Messages**
```typescript
import { withSuccessMessage } from '@/utils/apiResponseHandler';

const updateProfile = async (data: UpdateUserProfileRequest) => {
  const result = await withSuccessMessage(
    () => UserService.updateUserProfile(data),
    '资料更新成功',
    '资料更新失败'
  );
  
  if (result) {
    setUserData(result);
  }
};
```

**Pattern 3: Void Operations**
```typescript
import { withVoidResponse } from '@/utils/apiResponseHandler';

const deleteUser = async (userId: number) => {
  const success = await withVoidResponse(
    () => UserService.deleteUser(userId),
    '用户删除成功',
    '用户删除失败'
  );
  
  if (success) {
    refreshUserList();
  }
};
```

## Error Display Types

The system supports different error display methods:

```typescript
import { ErrorDisplayType } from '@/utils/errorHandler';

// Error message (default)
handleApiError(response, {
  displayType: ErrorDisplayType.ERROR
});

// Warning message
handleApiError(response, {
  displayType: ErrorDisplayType.WARNING
});

// Notification popup
handleApiError(response, {
  displayType: ErrorDisplayType.NOTIFICATION
});

// Silent (no display)
handleApiError(response, {
  displayType: ErrorDisplayType.SILENT
});
```

## Common Patterns

### 1. Form Submission

```typescript
const handleSubmit = async (values: FormData) => {
  setLoading(true);
  try {
    const response = await SomeService.submitForm(values);
    message.success('提交成功');
    form.resetFields();
  } catch (error) {
    // Error already handled by service layer
  } finally {
    setLoading(false);
  }
};
```

### 2. Data Fetching with Error States

```typescript
const [data, setData] = useState(null);
const [error, setError] = useState(null);
const [loading, setLoading] = useState(true);

useEffect(() => {
  const fetchData = async () => {
    try {
      setError(null);
      const result = await SomeService.getData();
      setData(result);
    } catch (err) {
      setError('数据加载失败');
      // Error message already shown by service layer
    } finally {
      setLoading(false);
    }
  };
  
  fetchData();
}, []);
```

### 3. Conditional Operations

```typescript
const handleAction = async () => {
  try {
    // Check permissions first
    const hasPermission = await AuthService.checkPermission();
    if (!hasPermission) {
      message.warning('权限不足');
      return;
    }
    
    // Perform action
    await SomeService.performAction();
    message.success('操作成功');
  } catch (error) {
    // Error already handled
  }
};
```

## Best Practices

### 1. Service Layer
- ✅ Always check `response.code` before returning `response.data`
- ✅ Use `handleApiError()` to display error messages
- ✅ Throw errors for the component layer to catch
- ✅ Use utility functions for common patterns

### 2. Component Layer
- ✅ Use try-catch blocks when calling services
- ✅ Don't manually check response codes (handled by services)
- ✅ Show loading states during API calls
- ✅ Handle success cases appropriately

### 3. Error Messages
- ✅ Use user-friendly error messages
- ✅ Provide context-specific error messages
- ✅ Use appropriate message types (error, warning, info)
- ✅ Don't expose technical details to users

## Migration Guide

To update existing code:

1. **Update Service Methods**:
   - Add response code checking
   - Use `handleApiError()` for error display
   - Throw errors on failure

2. **Update Components**:
   - Remove manual response code checking
   - Use try-catch for service calls
   - Remove manual error message display

3. **Use Utilities**:
   - Import response handler utilities
   - Use wrapper functions for common patterns
   - Leverage centralized error handling

## Examples

See the following files for complete examples:
- `src/services/auth.ts` - Authentication service with proper error handling
- `src/services/user.ts` - User service with response validation
- `src/pages/user/login/index.tsx` - Component using service layer properly
- `src/utils/apiResponseHandler.ts` - Response handling utilities
