package com.teammanage.exception;

import com.teammanage.common.ResponseCode;

/**
 * 速率限制异常
 *
 * 当用户请求频率超过限制时抛出。
 * 使用TOO_MANY_REQUESTS(429)响应代码。
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RateLimitException extends RuntimeException {

    private final Integer code;
    private final String limitType;
    private final Long retryAfter; // 重试等待时间（秒）
    private final Integer currentCount;
    private final Integer maxCount;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public RateLimitException(String message) {
        super(message);
        this.code = ResponseCode.TOO_MANY_REQUESTS;
        this.limitType = null;
        this.retryAfter = null;
        this.currentCount = null;
        this.maxCount = null;
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param limitType 限制类型
     * @param retryAfter 重试等待时间
     * @param currentCount 当前计数
     * @param maxCount 最大计数
     */
    public RateLimitException(String message, String limitType, Long retryAfter,
                             Integer currentCount, Integer maxCount) {
        super(message);
        this.code = ResponseCode.TOO_MANY_REQUESTS;
        this.limitType = limitType;
        this.retryAfter = retryAfter;
        this.currentCount = currentCount;
        this.maxCount = maxCount;
    }

    // Getter 方法
    public Integer getCode() {
        return code;
    }

    public String getLimitType() {
        return limitType;
    }

    public Long getRetryAfter() {
        return retryAfter;
    }

    public Integer getCurrentCount() {
        return currentCount;
    }

    public Integer getMaxCount() {
        return maxCount;
    }

    // 静态工厂方法，用于创建常见的速率限制异常

    /**
     * 创建API调用频率限制异常
     * 
     * @param retryAfter 重试等待时间（秒）
     * @param currentCount 当前计数
     * @param maxCount 最大计数
     * @return 异常实例
     */
    public static RateLimitException apiCallLimit(Long retryAfter, Integer currentCount, Integer maxCount) {
        return new RateLimitException(
            String.format("API调用频率过高，请%d秒后重试", retryAfter),
            "API_CALL",
            retryAfter,
            currentCount,
            maxCount
        );
    }

    /**
     * 创建邀请频率限制异常
     * 
     * @param retryAfter 重试等待时间（秒）
     * @return 异常实例
     */
    public static RateLimitException invitationLimit(Long retryAfter) {
        return new RateLimitException(
            String.format("邀请发送过于频繁，请%d秒后重试", retryAfter),
            "INVITATION",
            retryAfter,
            null,
            null
        );
    }

    /**
     * 创建登录尝试频率限制异常
     * 
     * @param retryAfter 重试等待时间（秒）
     * @param currentCount 当前尝试次数
     * @param maxCount 最大尝试次数
     * @return 异常实例
     */
    public static RateLimitException loginAttemptLimit(Long retryAfter, Integer currentCount, Integer maxCount) {
        return new RateLimitException(
            String.format("登录尝试次数过多，账号已锁定%d秒", retryAfter),
            "LOGIN_ATTEMPT",
            retryAfter,
            currentCount,
            maxCount
        );
    }

    /**
     * 创建团队创建频率限制异常
     * 
     * @param retryAfter 重试等待时间（秒）
     * @return 异常实例
     */
    public static RateLimitException teamCreationLimit(Long retryAfter) {
        return new RateLimitException(
            String.format("团队创建过于频繁，请%d秒后重试", retryAfter),
            "TEAM_CREATION",
            retryAfter,
            null,
            null
        );
    }

    @Override
    public String toString() {
        return "RateLimitException{" +
                "code=" + code +
                ", limitType='" + limitType + '\'' +
                ", retryAfter=" + retryAfter +
                ", currentCount=" + currentCount +
                ", maxCount=" + maxCount +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
