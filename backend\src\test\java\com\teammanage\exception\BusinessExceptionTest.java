package com.teammanage.exception;

import com.teammanage.common.ResponseCode;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 业务异常测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BusinessExceptionTest {

    @Test
    void testDefaultConstructor() {
        String message = "测试错误消息";
        BusinessException exception = new BusinessException(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.BAD_REQUEST, exception.getCode());
    }

    @Test
    void testConstructorWithCode() {
        String message = "自定义错误消息";
        Integer code = ResponseCode.FORBIDDEN;
        BusinessException exception = new BusinessException(code, message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
    }

    @Test
    void testConstructorWithCause() {
        String message = "带原因的错误消息";
        Throwable cause = new RuntimeException("原始异常");
        BusinessException exception = new BusinessException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.BAD_REQUEST, exception.getCode());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testConstructorWithCodeAndCause() {
        String message = "完整构造函数测试";
        Integer code = ResponseCode.NOT_FOUND;
        Throwable cause = new RuntimeException("原始异常");
        BusinessException exception = new BusinessException(code, message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(code, exception.getCode());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testBadRequestFactory() {
        String message = "参数错误";
        BusinessException exception = BusinessException.badRequest(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.BAD_REQUEST, exception.getCode());
    }

    @Test
    void testUnauthorizedFactory() {
        String message = "未认证";
        BusinessException exception = BusinessException.unauthorized(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.UNAUTHORIZED, exception.getCode());
    }

    @Test
    void testForbiddenFactory() {
        String message = "权限不足";
        BusinessException exception = BusinessException.forbidden(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.FORBIDDEN, exception.getCode());
    }

    @Test
    void testNotFoundFactory() {
        String message = "资源不存在";
        BusinessException exception = BusinessException.notFound(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.NOT_FOUND, exception.getCode());
    }

    @Test
    void testConflictFactory() {
        String message = "资源冲突";
        BusinessException exception = BusinessException.conflict(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.CONFLICT, exception.getCode());
    }

    @Test
    void testUnprocessableEntityFactory() {
        String message = "请求语义错误";
        BusinessException exception = BusinessException.unprocessableEntity(message);
        
        assertEquals(message, exception.getMessage());
        assertEquals(ResponseCode.UNPROCESSABLE_ENTITY, exception.getCode());
    }

    @Test
    void testExceptionInheritance() {
        BusinessException exception = new BusinessException("测试");
        
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    void testExceptionChaining() {
        RuntimeException originalException = new RuntimeException("原始异常");
        BusinessException businessException = new BusinessException("业务异常", originalException);
        
        assertEquals("业务异常", businessException.getMessage());
        assertEquals(originalException, businessException.getCause());
        assertEquals("原始异常", businessException.getCause().getMessage());
    }
}
