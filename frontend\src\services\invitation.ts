/**
 * 团队邀请相关 API 服务
 */

import type {
  TeamInvitationResponse,
  RespondInvitationRequest,
  SendInvitationResponse,
  AcceptInvitationByLinkRequest,
  AcceptInvitationByLinkResponse,
  InvitationInfoResponse,
} from '@/types/api';
import { apiRequest } from '@/utils/request';
import { ResponseCode } from '@/constants/responseCodes';
import { handleApiError } from '@/utils/errorHandler';

/**
 * 邀请服务类
 */
export class InvitationService {
  /**
   * 获取当前团队的邀请列表（需要 Team Token，仅创建者）
   */
  static async getCurrentTeamInvitations(): Promise<TeamInvitationResponse[]> {
    const response = await apiRequest.get<TeamInvitationResponse[]>('/teams/current/invitations');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 获取用户收到的邀请列表（需要 Account Token）
   */
  static async getUserReceivedInvitations(): Promise<TeamInvitationResponse[]> {
    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/received');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 获取用户收到的待处理邀请列表（需要 Account Token）
   */
  static async getUserPendingInvitations(): Promise<TeamInvitationResponse[]> {
    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/pending');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 响应邀请（需要 Account Token）
   */
  static async respondToInvitation(
    invitationId: number,
    data: RespondInvitationRequest,
  ): Promise<void> {
    const response = await apiRequest.post<void>(`/invitations/${invitationId}/respond`, data);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }
  }

  /**
   * 取消邀请（需要 Team Token，仅邀请人）
   */
  static async cancelInvitation(invitationId: number): Promise<void> {
    const response = await apiRequest.delete<void>(`/invitations/${invitationId}`);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }
  }

  /**
   * 获取邀请详情
   */
  static async getInvitationDetail(invitationId: number): Promise<TeamInvitationResponse> {
    const response = await apiRequest.get<TeamInvitationResponse>(`/invitations/${invitationId}`);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 发送邀请并生成邀请链接（需要 Team Token，仅创建者）
   */
  static async sendInvitations(data: { emails: string[]; message?: string }): Promise<SendInvitationResponse> {
    const response = await apiRequest.post<SendInvitationResponse>('/invitations/send', data);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 获取邀请信息（公开接口）
   */
  static async getInvitationInfo(token: string): Promise<InvitationInfoResponse> {
    const response = await apiRequest.get<InvitationInfoResponse>(
      `/invitations/info/${token}`
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 通过邀请链接接受邀请（公开接口）
   */
  static async acceptInvitationByLink(
    token: string,
    data: AcceptInvitationByLinkRequest
  ): Promise<AcceptInvitationByLinkResponse> {
    const response = await apiRequest.post<AcceptInvitationByLinkResponse>(
      `/invitations/accept-by-link/${token}`,
      data
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 更新过期邀请状态（系统内部接口）
   */
  static async updateExpiredInvitations(): Promise<number> {
    const response = await apiRequest.post<number>('/invitations/system/update-expired');
    return response.data;
  }
}

// 默认导出
export default InvitationService;
