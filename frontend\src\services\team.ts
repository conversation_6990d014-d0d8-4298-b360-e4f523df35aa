/**
 * 团队管理相关 API 服务
 */

import type {
  CreateTeamRequest,
  InviteMembersRequest,
  PageRequest,
  PageResponse,
  TeamDetailResponse,
  TeamMemberResponse,
  UpdateTeamRequest,
} from '@/types/api';
import { apiRequest } from '@/utils/request';
import { ResponseCode } from '@/constants/responseCodes';
import { handleApiError } from '@/utils/errorHandler';

/**
 * 团队服务类
 *
 * 提供团队相关的所有API接口，包括：
 * - 团队创建和管理
 * - 团队成员管理
 * - 团队邀请功能
 * - 团队统计信息
 *
 * 权限说明：
 * - Account Token：用户级别操作（创建团队、获取用户团队列表）
 * - Team Token：团队级别操作（团队详情、成员管理等）
 * - 创建者权限：某些操作仅团队创建者可执行
 *
 * <AUTHOR>
 * @since 1.0.0
 */
export class TeamService {
  /**
   * 创建团队
   *
   * 创建新的团队，创建者自动成为团队管理员。
   * 需要用户级别的Token（Account Token）。
   *
   * @param data 团队创建请求参数
   * @param data.name 团队名称（必填，2-50字符）
   * @param data.description 团队描述（可选）
   * @returns Promise<TeamDetailResponse> 创建的团队信息
   * @throws 当团队名称重复或用户权限不足时抛出异常
   *
   * @example
   * ```typescript
   * const newTeam = await TeamService.createTeam({
   *   name: '开发团队',
   *   description: '负责产品开发的团队'
   * });
   * console.log('团队创建成功:', newTeam.name);
   * ```
   */
  static async createTeam(
    data: CreateTeamRequest,
  ): Promise<TeamDetailResponse> {
    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 获取用户的团队列表
   *
   * 获取当前用户所属的所有团队的基本信息。
   * 需要用户级别的Token（Account Token）。
   *
   * @returns Promise<TeamDetailResponse[]> 团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeams();
   * console.log('用户所属团队数量:', teams.length);
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, ID: ${team.id}`);
   * });
   * ```
   */
  static async getUserTeams(): Promise<TeamDetailResponse[]> {
    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 获取用户的团队列表（包含统计数据）
   *
   * 获取当前用户所属的所有团队，包含每个团队的统计信息。
   * 用于个人中心的团队列表展示。
   *
   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeamsWithStats();
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);
   * });
   * ```
   */
  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {
    const response = await apiRequest.get<TeamDetailResponse[]>(
      '/teams?includeStats=true',
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 获取当前团队详情
   *
   * 获取当前选择团队的详细信息。
   * 需要团队级别的Token（Team Token）。
   *
   * @returns Promise<TeamDetailResponse> 团队详细信息
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const teamDetail = await TeamService.getCurrentTeamDetail();
   * console.log('当前团队:', teamDetail.name);
   * console.log('团队描述:', teamDetail.description);
   * console.log('成员数量:', teamDetail.memberCount);
   * ```
   */
  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {
    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 更新当前团队信息
   *
   * 更新当前团队的基本信息，如名称、描述等。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 团队更新请求参数
   * @param data.name 新的团队名称（可选）
   * @param data.description 新的团队描述（可选）
   * @returns Promise<TeamDetailResponse> 更新后的团队信息
   * @throws 当用户非团队创建者或团队名称重复时抛出异常
   *
   * @example
   * ```typescript
   * const updatedTeam = await TeamService.updateCurrentTeam({
   *   name: '新团队名称',
   *   description: '更新后的团队描述'
   * });
   * console.log('团队信息更新成功');
   * ```
   */
  static async updateCurrentTeam(
    data: UpdateTeamRequest,
  ): Promise<TeamDetailResponse> {
    const response = await apiRequest.put<TeamDetailResponse>(
      '/teams/current',
      data,
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 删除当前团队（需要 Team Token，仅创建者）
   *
   * 权限要求：
   * - 需要有效的Team Token
   * - 只有团队创建者可以执行此操作
   *
   * 删除效果：
   * - 软删除团队记录
   * - 级联删除所有团队成员关系
   * - 不可恢复
   *
   * @returns Promise<void> 删除成功时resolve
   * @throws 当权限不足或团队不存在时抛出异常
   */
  static async deleteCurrentTeam(): Promise<void> {
    const response = await apiRequest.delete<string>('/teams/current');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }
  }

  /**
   * 获取当前团队成员列表（简单数组格式）
   *
   * 获取当前团队的所有成员，返回简单数组格式。
   * 内部调用分页接口并获取所有成员。
   *
   * @returns Promise<TeamMemberResponse[]> 团队成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const members = await TeamService.getCurrentTeamMembers();
   * console.log('团队成员数量:', members.length);
   * members.forEach(member => {
   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);
   * });
   * ```
   */
  static async getCurrentTeamMembers(): Promise<TeamMemberResponse[]> {
    const response = await TeamService.getTeamMembers({
      current: 1,
      pageSize: 1000, // 获取大量数据以确保包含所有成员
    });
    return response?.list || [];
  }

  /**
   * 获取当前团队成员列表（分页格式）
   *
   * 获取当前团队的成员列表，支持分页查询。
   * 需要团队级别的Token（Team Token）。
   *
   * @param params 分页查询参数（可选）
   * @param params.current 当前页码（默认1）
   * @param params.pageSize 每页大小（默认10）
   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const membersPage = await TeamService.getTeamMembers({
   *   current: 1,
   *   pageSize: 20
   * });
   *
   * console.log('总成员数:', membersPage.total);
   * console.log('当前页成员:', membersPage.list);
   * ```
   */
  static async getTeamMembers(
    params?: PageRequest,
  ): Promise<PageResponse<TeamMemberResponse>> {
    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(
      '/teams/current/members',
      params,
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 邀请团队成员
   *
   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 邀请请求参数
   * @param data.emails 被邀请人的邮箱列表
   * @returns Promise<void> 邀请发送成功时resolve
   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.inviteMembers({
   *   emails: ['<EMAIL>', '<EMAIL>']
   * });
   * console.log('邀请已发送');
   * ```
   */
  static async inviteMembers(data: InviteMembersRequest): Promise<void> {
    const response = await apiRequest.post<void>(
      '/teams/current/members/invite',
      data,
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 移除团队成员
   *
   * 从当前团队中移除指定成员。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 要移除的成员ID
   * @returns Promise<void> 移除成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.removeMember(123);
   * console.log('成员已移除');
   * ```
   */
  static async removeMember(memberId: number): Promise<void> {
    const response = await apiRequest.delete<void>(
      `/teams/current/members/${memberId}`,
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 更新团队成员状态
   *
   * 更新团队成员的激活状态（启用/禁用）。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 成员ID
   * @param isActive 是否激活（true=启用，false=禁用）
   * @returns Promise<void> 更新成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * // 禁用成员
   * await TeamService.updateMemberStatus(123, false);
   * console.log('成员已禁用');
   *
   * // 启用成员
   * await TeamService.updateMemberStatus(123, true);
   * console.log('成员已启用');
   * ```
   */
  static async updateMemberStatus(memberId: number, isActive: boolean): Promise<void> {
    const response = await apiRequest.put<void>(
      `/teams/current/members/${memberId}/status?isActive=${isActive}`,
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }



  /**
   * 获取团队统计信息
   *
   * 获取当前团队的统计信息，包括成员数量、活跃成员数等。
   * 注意：当前通过团队详情和成员列表计算，等待后端提供专门的统计接口。
   *
   * @returns Promise<object> 团队统计信息
   * @returns Promise<object>.memberCount 总成员数
   * @returns Promise<object>.activeMembers 活跃成员数（状态为启用的成员）
   * @returns Promise<object>.recentActivity 最近活跃成员数（7天内有访问的成员）
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const stats = await TeamService.getTeamStats();
   * console.log('总成员数:', stats.memberCount);
   * console.log('活跃成员数:', stats.activeMembers);
   * console.log('最近活跃成员数:', stats.recentActivity);
   * ```
   */
  static async getTeamStats(): Promise<{
    memberCount: number;
    activeMembers: number;
    recentActivity: number;
  }> {
    // 这里可能需要后端提供专门的统计接口
    // 暂时通过团队详情和成员列表来计算
    const teamDetail = await TeamService.getCurrentTeamDetail();
    const members = await TeamService.getTeamMembers({
      current: 1,
      pageSize: 1000,
    });

    const activeMembers = members.list.filter(
      (member) => member.isActive,
    ).length;
    const recentActivity = members.list.filter((member) => {
      const lastAccess = new Date(member.lastAccessTime);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return lastAccess > weekAgo;
    }).length;

    return {
      memberCount: teamDetail.memberCount,
      activeMembers,
      recentActivity,
    };
  }


}

// 导出默认实例
export default TeamService;
