/**
 * TODO服务
 */

import type {
  CreateTodoRequest,
  TodoResponse,
  TodoStatsResponse,
  UpdateTodoRequest,
} from '@/types/api';
import { apiRequest } from '@/utils/request';
import { ResponseCode } from '@/constants/responseCodes';
import { handleApiError } from '@/utils/errorHandler';

export class TodoService {
  /**
   * 获取用户的TODO列表
   */
  static async getUserTodos(): Promise<TodoResponse[]> {
    const response = await apiRequest.get<TodoResponse[]>('/todos');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 创建TODO
   */
  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {
    const response = await apiRequest.post<TodoResponse>('/todos', request);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 更新TODO
   */
  static async updateTodo(
    id: number,
    request: UpdateTodoRequest,
  ): Promise<TodoResponse> {
    const response = await apiRequest.put<TodoResponse>(
      `/todos/${id}`,
      request,
    );

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }

  /**
   * 删除TODO
   */
  static async deleteTodo(id: number): Promise<void> {
    const response = await apiRequest.delete(`/todos/${id}`);

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }
  }

  /**
   * 获取TODO统计信息
   */
  static async getTodoStats(): Promise<TodoStatsResponse> {
    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');

    // Check response code and handle errors
    if (!ResponseCode.isSuccess(response.code)) {
      handleApiError(response);
      throw new Error(response.message);
    }

    return response.data;
  }
}
