package com.teammanage.common;

/**
 * 标准化响应代码常量
 * 
 * 定义系统中所有标准响应代码，确保前后端一致性。
 * 所有API响应都应该使用这些预定义的响应代码。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ResponseCode {

    // ============= 成功状态码 =============
    
    /**
     * 操作成功
     */
    public static final int SUCCESS = 200;

    // ============= 客户端错误状态码 =============
    
    /**
     * 请求参数错误或业务逻辑错误
     */
    public static final int BAD_REQUEST = 400;
    
    /**
     * 未认证，需要登录
     */
    public static final int UNAUTHORIZED = 401;
    
    /**
     * 权限不足，已认证但无权限访问
     */
    public static final int FORBIDDEN = 403;
    
    /**
     * 资源不存在
     */
    public static final int NOT_FOUND = 404;
    
    /**
     * 资源冲突（如重复创建、数据冲突等）
     */
    public static final int CONFLICT = 409;
    
    /**
     * 请求格式正确但语义错误（如验证失败）
     */
    public static final int UNPROCESSABLE_ENTITY = 422;
    
    /**
     * 请求频率限制
     */
    public static final int TOO_MANY_REQUESTS = 429;

    // ============= 服务器错误状态码 =============
    
    /**
     * 服务器内部错误
     */
    public static final int INTERNAL_SERVER_ERROR = 500;
    
    /**
     * 网关错误
     */
    public static final int BAD_GATEWAY = 502;
    
    /**
     * 服务不可用
     */
    public static final int SERVICE_UNAVAILABLE = 503;
    
    /**
     * 网关超时
     */
    public static final int GATEWAY_TIMEOUT = 504;

    // ============= 响应代码分类方法 =============
    
    /**
     * 判断是否为成功响应代码
     * 
     * @param code 响应代码
     * @return 是否为成功代码
     */
    public static boolean isSuccess(int code) {
        return code == SUCCESS;
    }
    
    /**
     * 判断是否为客户端错误代码
     * 
     * @param code 响应代码
     * @return 是否为客户端错误代码
     */
    public static boolean isClientError(int code) {
        return code >= 400 && code < 500;
    }
    
    /**
     * 判断是否为服务器错误代码
     * 
     * @param code 响应代码
     * @return 是否为服务器错误代码
     */
    public static boolean isServerError(int code) {
        return code >= 500 && code < 600;
    }
    
    /**
     * 判断是否为错误代码（非成功代码）
     * 
     * @param code 响应代码
     * @return 是否为错误代码
     */
    public static boolean isError(int code) {
        return !isSuccess(code);
    }

    // ============= 响应代码描述方法 =============
    
    /**
     * 获取响应代码的默认描述
     * 
     * @param code 响应代码
     * @return 响应代码描述
     */
    public static String getDescription(int code) {
        switch (code) {
            case SUCCESS:
                return "操作成功";
            case BAD_REQUEST:
                return "请求参数错误";
            case UNAUTHORIZED:
                return "未认证，需要登录";
            case FORBIDDEN:
                return "权限不足";
            case NOT_FOUND:
                return "资源不存在";
            case CONFLICT:
                return "资源冲突";
            case UNPROCESSABLE_ENTITY:
                return "请求语义错误";
            case TOO_MANY_REQUESTS:
                return "请求频率过高";
            case INTERNAL_SERVER_ERROR:
                return "服务器内部错误";
            case BAD_GATEWAY:
                return "网关错误";
            case SERVICE_UNAVAILABLE:
                return "服务不可用";
            case GATEWAY_TIMEOUT:
                return "网关超时";
            default:
                return "未知错误";
        }
    }

    // 私有构造函数，防止实例化
    private ResponseCode() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
