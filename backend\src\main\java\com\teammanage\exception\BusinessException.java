package com.teammanage.exception;

import com.teammanage.common.ResponseCode;

/**
 * 业务异常
 *
 * 用于表示业务逻辑错误，默认使用BAD_REQUEST(400)响应代码。
 * 可以通过构造函数指定特定的响应代码。
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class BusinessException extends RuntimeException {

    private final Integer code;

    /**
     * 创建业务异常，使用默认的BAD_REQUEST响应代码
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = ResponseCode.BAD_REQUEST;
    }

    /**
     * 创建业务异常，指定响应代码
     *
     * @param code 响应代码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 创建业务异常，使用默认的BAD_REQUEST响应代码
     *
     * @param message 错误消息
     * @param cause 原因
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResponseCode.BAD_REQUEST;
    }

    /**
     * 创建业务异常，指定响应代码
     *
     * @param code 响应代码
     * @param message 错误消息
     * @param cause 原因
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    // ============= 静态工厂方法 =============

    /**
     * 创建参数错误异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException badRequest(String message) {
        return new BusinessException(ResponseCode.BAD_REQUEST, message);
    }

    /**
     * 创建未认证异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(ResponseCode.UNAUTHORIZED, message);
    }

    /**
     * 创建权限不足异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(ResponseCode.FORBIDDEN, message);
    }

    /**
     * 创建资源不存在异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(ResponseCode.NOT_FOUND, message);
    }

    /**
     * 创建资源冲突异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException conflict(String message) {
        return new BusinessException(ResponseCode.CONFLICT, message);
    }

    /**
     * 创建请求语义错误异常
     *
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException unprocessableEntity(String message) {
        return new BusinessException(ResponseCode.UNPROCESSABLE_ENTITY, message);
    }

}
