import {
  CalendarOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  CrownOutlined,
  LogoutOutlined,
  MailOutlined,
  MenuOutlined,
  PhoneOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SaveOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import {
  Button,
  Card,
  FloatButton,
  Form,
  Input,
  Modal,
  message,
  Space,
  Tabs,
  Tag,
  Typography,
} from 'antd';
import React, { useState } from 'react';
import SubscriptionPlansContent from '@/pages/subscription/components/SubscriptionPlansContent';
import { AuthService, SubscriptionService, UserService } from '@/services';
import { TeamService } from '@/services/team';
import type {
  CreateTeamRequest,
  SubscriptionResponse,
  UpdateUserProfileRequest,
} from '@/types/api';

// const { Title } = Typography;

interface UserFloatButtonProps {
  style?: React.CSSProperties;
}

const UserFloatButton: React.FC<UserFloatButtonProps> = ({ style }) => {
  const [open, setOpen] = useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [profileForm] = Form.useForm();
  const [profileLoading, setProfileLoading] = useState(false);
  const [currentSubscription, setCurrentSubscription] =
    useState<SubscriptionResponse | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);

  // 创建团队相关状态
  const [createTeamForm] = Form.useForm();
  const [createTeamLoading, setCreateTeamLoading] = useState(false);

  const { initialState, setInitialState } = useModel('@@initialState');

  // 如果用户未登录，不显示浮动按钮
  // 确保用户已登录后就显示 FloatButton，无需等待团队选择
  if (!initialState?.currentUser) {
    return null;
  }

  // 处理个人中心跳转
  const handlePersonalCenter = () => {
    setOpen(false);
    history.push('/personal-center');
  };

  // 处理设置
  const handleSettings = async () => {
    setOpen(false);
    if (initialState?.currentUser) {
      profileForm.setFieldsValue({
        name: initialState.currentUser.name,
        email: initialState.currentUser.email,
        telephone: initialState.currentUser.telephone || '',
      });
    }

    // 获取当前订阅信息
    setSubscriptionLoading(true);
    try {
      const subscription = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('获取当前订阅失败:', error);
      setCurrentSubscription(null);
    } finally {
      setSubscriptionLoading(false);
    }

    setSettingsModalVisible(true);
  };

  // 处理帮助文档
  const handleHelp = () => {
    setOpen(false);
    history.push('/help');
  };

  // 保存用户资料
  const handleSaveProfile = async (values: any) => {
    try {
      setProfileLoading(true);
      const updateData: UpdateUserProfileRequest = {
        name: values.name,
        telephone: values.telephone,
      };

      const updatedProfile = await UserService.updateUserProfile(updateData);

      // 更新 initialState 中的用户信息
      await setInitialState((prevState) => ({
        ...prevState,
        currentUser: {
          ...prevState?.currentUser,
          ...updatedProfile,
        },
      }));

      message.success('个人资料更新成功');
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新个人资料失败');
    } finally {
      setProfileLoading(false);
    }
  };

  // 处理订阅成功
  const handleSubscriptionSuccess = async () => {
    // 刷新当前订阅信息
    try {
      const subscription = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('刷新订阅信息失败:', error);
    }

    setSettingsModalVisible(false);
    message.success('订阅成功！');
  };

  // 创建团队处理函数
  const handleCreateTeam = async (values: CreateTeamRequest) => {
    setCreateTeamLoading(true);
    try {
      await TeamService.createTeam(values);
      message.success('团队创建成功！');

      // 重置表单
      createTeamForm.resetFields();

      // 可以选择关闭模态框或切换到其他标签页
      message.info('团队创建成功，您可以在个人中心查看团队列表');
    } catch (error) {
      console.error('创建团队失败:', error);
      message.error('创建团队失败，请重试');
    } finally {
      setCreateTeamLoading(false);
    }
  };

  // 计算剩余天数
  const calculateRemainingDays = (endDate: string): number => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  // 处理退出登录
  const handleLogout = async () => {
    try {
      setLogoutLoading(true);

      // 调用退出登录API
      await AuthService.logout();

      // 清除 initialState
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }

      // 跳转到登录页面
      history.push('/user/login');
      message.success('已成功退出登录');
    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也要清除本地状态并跳转
      if (setInitialState) {
        await setInitialState({
          currentUser: undefined,
          currentTeam: undefined,
        });
      }
      history.push('/user/login');
      message.warning('退出登录完成');
    } finally {
      setLogoutLoading(false);
      setLogoutModalVisible(false);
      setOpen(false);
    }
  };

  const floatButtonItems = [
    {
      key: 'personal-center',
      icon: <UserOutlined />,
      tooltip: '个人中心',
      onClick: handlePersonalCenter,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      tooltip: '设置',
      onClick: handleSettings,
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      tooltip: '帮助文档',
      onClick: handleHelp,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      tooltip: '退出登录',
      onClick: () => {
        setOpen(false);
        setLogoutModalVisible(true);
      },
    },
  ];

  return (
    <>
      <FloatButton.Group
        open={open}
        trigger="click"
        type="primary"
        placement="left"
        style={{
          right: 24,
          bottom: 24,
          ...style,
        }}
        icon={open ? <CloseOutlined /> : <MenuOutlined />}
        onClick={() => setOpen(!open)}
      >
        {floatButtonItems.map((item) => (
          <FloatButton
            key={item.key}
            icon={item.icon}
            tooltip={item.tooltip}
            onClick={item.onClick}
          />
        ))}
      </FloatButton.Group>

      {/* 统一设置模态框 */}
      <Modal
        title={
          <Space>
            <SettingOutlined />
            设置
          </Space>
        }
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
        width={1200}
        style={{ top: 20 }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'profile',
              label: '个人资料',
              children: (
                <div style={{ padding: '20px 0' }}>
                  {/* 表单部分 */}
                  <Form
                    form={profileForm}
                    layout="vertical"
                    onFinish={handleSaveProfile}
                  >
                    <Form.Item
                      label="用户名"
                      name="name"
                      rules={[
                        { required: true, message: '请输入用户名' },
                        { max: 100, message: '用户名不能超过100个字符' },
                      ]}
                    >
                      <Input
                        prefix={<UserOutlined />}
                        placeholder="请输入用户名"
                      />
                    </Form.Item>

                    <Form.Item label="邮箱地址" name="email">
                      <Input
                        prefix={<MailOutlined />}
                        disabled
                        placeholder="邮箱地址不可修改"
                      />
                    </Form.Item>

                    <Form.Item
                      label="手机号"
                      name="telephone"
                      rules={[
                        {
                          pattern: /^1[3-9]\d{9}$/,
                          message: '请输入正确的手机号格式',
                        },
                      ]}
                    >
                      <Input
                        prefix={<PhoneOutlined />}
                        placeholder="请输入手机号"
                        maxLength={11}
                      />
                    </Form.Item>

                    <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                      <Space>
                        <Button onClick={() => setSettingsModalVisible(false)}>
                          取消
                        </Button>
                        <Button
                          type="primary"
                          htmlType="submit"
                          loading={profileLoading}
                          icon={<SaveOutlined />}
                        >
                          保存修改
                        </Button>
                      </Space>
                    </Form.Item>
                  </Form>
                </div>
              ),
            },
            {
              key: 'subscription',
              label: '订阅套餐',
              children: (
                <div style={{ padding: '20px 0' }}>
                  {/* 当前套餐信息 */}
                  <div style={{ marginBottom: 24 }}>
                    <Typography.Title level={4} style={{ marginBottom: 16 }}>
                      <CrownOutlined
                        style={{ marginRight: 8, color: '#faad14' }}
                      />
                      当前套餐
                    </Typography.Title>

                    {subscriptionLoading ? (
                      <div style={{ textAlign: 'center', padding: '20px 0' }}>
                        加载中...
                      </div>
                    ) : currentSubscription ? (
                      <Card
                        size="small"
                        style={{
                          background: '#f6ffed',
                          border: '1px solid #b7eb8f',
                          marginBottom: 16,
                        }}
                      >
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                            }}
                          >
                            <Typography.Text strong style={{ fontSize: 16 }}>
                              {currentSubscription.planName}
                            </Typography.Text>
                            <Tag
                              color={
                                currentSubscription.status === 'ACTIVE'
                                  ? 'green'
                                  : 'orange'
                              }
                            >
                              {currentSubscription.status === 'ACTIVE'
                                ? '有效'
                                : '已过期'}
                            </Tag>
                          </div>

                          <Typography.Text type="secondary">
                            {currentSubscription.planDescription}
                          </Typography.Text>

                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                            }}
                          >
                            <Space>
                              <CalendarOutlined />
                              <span>
                                到期时间:{' '}
                                {new Date(
                                  currentSubscription.endDate,
                                ).toLocaleDateString()}
                              </span>
                            </Space>
                            <Space>
                              <ClockCircleOutlined />
                              <span>
                                剩余:{' '}
                                {calculateRemainingDays(
                                  currentSubscription.endDate,
                                )}{' '}
                                天
                              </span>
                            </Space>
                          </div>
                        </Space>
                      </Card>
                    ) : (
                      <Card
                        size="small"
                        style={{
                          background: '#fff2e8',
                          border: '1px solid #ffbb96',
                          marginBottom: 16,
                        }}
                      >
                        <Typography.Text type="secondary">
                          您当前没有有效的订阅套餐，请选择合适的套餐进行订阅
                        </Typography.Text>
                      </Card>
                    )}
                  </div>

                  {/* 可选套餐列表 */}
                  <div>
                    <Typography.Title level={4} style={{ marginBottom: 16 }}>
                      选择套餐
                    </Typography.Title>
                    <SubscriptionPlansContent
                      onSubscriptionSuccess={handleSubscriptionSuccess}
                    />
                  </div>
                </div>
              ),
            },
            {
              key: 'team',
              label: '团队管理',
              children: (
                <div style={{ padding: '20px 0' }}>
                  <Typography.Title level={4} style={{ marginBottom: 16 }}>
                    <TeamOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                    创建新团队
                  </Typography.Title>

                  <Form
                    form={createTeamForm}
                    layout="vertical"
                    onFinish={handleCreateTeam}
                    autoComplete="off"
                  >
                    <Form.Item
                      label="团队名称"
                      name="name"
                      rules={[
                        { required: true, message: '请输入团队名称' },
                        { min: 2, message: '团队名称至少2个字符' },
                        { max: 50, message: '团队名称不能超过50个字符' },
                      ]}
                    >
                      <Input
                        prefix={<TeamOutlined />}
                        placeholder="请输入团队名称"
                        maxLength={50}
                      />
                    </Form.Item>

                    <Form.Item
                      label="团队描述"
                      name="description"
                      rules={[
                        { max: 200, message: '团队描述不能超过200个字符' },
                      ]}
                    >
                      <Input.TextArea
                        placeholder="请输入团队描述（可选）"
                        rows={4}
                        maxLength={200}
                        showCount
                      />
                    </Form.Item>

                    <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                      <Space>
                        <Button
                          onClick={() => {
                            createTeamForm.resetFields();
                          }}
                        >
                          重置
                        </Button>
                        <Button
                          type="primary"
                          htmlType="submit"
                          loading={createTeamLoading}
                          icon={<PlusOutlined />}
                        >
                          创建团队
                        </Button>
                      </Space>
                    </Form.Item>
                  </Form>
                </div>
              ),
            },
          ]}
        />
      </Modal>

      {/* 退出登录确认模态框 */}
      <Modal
        title="确认退出登录"
        open={logoutModalVisible}
        onCancel={() => setLogoutModalVisible(false)}
        onOk={handleLogout}
        confirmLoading={logoutLoading}
        okText="确认退出"
        cancelText="取消"
        okButtonProps={{ danger: true }}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <LogoutOutlined
            style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }}
          />
          <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>
            您确定要退出登录吗？
          </div>
          <div style={{ color: '#666', fontSize: 14 }}>
            退出后您需要重新登录才能继续使用系统
          </div>
        </div>
      </Modal>
    </>
  );
};

export default UserFloatButton;
