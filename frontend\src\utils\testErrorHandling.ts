/**
 * 错误处理测试工具
 * 
 * 用于测试和验证错误处理机制是否正常工作
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import { handleApiError } from './errorHandler';
import { ResponseCode } from '@/constants/responseCodes';
import type { ApiResponse } from '@/types/api';

/**
 * 测试403错误处理
 */
export const test403Error = () => {
  const mockResponse: ApiResponse<any> = {
    code: ResponseCode.FORBIDDEN,
    message: '您的账户已在此团队中被停用',
    data: null,
    timestamp: new Date().toISOString(),
  };

  console.log('测试403错误处理:', mockResponse);
  handleApiError(mockResponse);
};

/**
 * 测试各种错误代码
 */
export const testAllErrorCodes = () => {
  const testCases = [
    {
      code: ResponseCode.BAD_REQUEST,
      message: '请求参数错误',
    },
    {
      code: ResponseCode.UNAUTHORIZED,
      message: '登录已过期，请重新登录',
    },
    {
      code: ResponseCode.FORBIDDEN,
      message: '您的账户已在此团队中被停用',
    },
    {
      code: ResponseCode.NOT_FOUND,
      message: '团队不存在',
    },
    {
      code: ResponseCode.CONFLICT,
      message: '邮箱已被注册',
    },
    {
      code: ResponseCode.UNPROCESSABLE_ENTITY,
      message: '验证码错误',
    },
    {
      code: ResponseCode.TOO_MANY_REQUESTS,
      message: '请求频率过高，请60秒后重试',
    },
    {
      code: ResponseCode.INTERNAL_SERVER_ERROR,
      message: '数据库连接失败',
    },
  ];

  testCases.forEach((testCase, index) => {
    setTimeout(() => {
      const mockResponse: ApiResponse<any> = {
        code: testCase.code,
        message: testCase.message,
        data: null,
        timestamp: new Date().toISOString(),
      };

      console.log(`测试错误代码 ${testCase.code}:`, mockResponse);
      handleApiError(mockResponse);
    }, index * 1000); // 每秒显示一个错误
  });
};

/**
 * 在浏览器控制台中暴露测试函数
 */
if (typeof window !== 'undefined') {
  (window as any).testErrorHandling = {
    test403Error,
    testAllErrorCodes,
  };
  
  console.log('错误处理测试工具已加载，可以在控制台中使用：');
  console.log('- window.testErrorHandling.test403Error() // 测试403错误');
  console.log('- window.testErrorHandling.testAllErrorCodes() // 测试所有错误代码');
}
