/**
 * 响应代码常量测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import {
  ResponseCode,
  SUCCESS,
  BAD_REQUEST,
  UNAUTHORIZED,
  FORBIDDEN,
  NOT_FOUND,
  CONFLICT,
  UNPROCESSABLE_ENTITY,
  TOO_MANY_REQUESTS,
  INTERNAL_SERVER_ERROR,
  BAD_GATEWAY,
  SERVICE_UNAVAILABLE,
  GATEWAY_TIMEOUT,
  isSuccess,
  isClientError,
  isServerError,
  isError,
  getDescription,
  SUCCESS_CODES,
  CLIENT_ERROR_CODES,
  SERVER_ERROR_CODES,
  ERROR_CODES,
  ALL_CODES,
} from '../responseCodes';

describe('ResponseCode Constants', () => {
  test('should have correct success code', () => {
    expect(SUCCESS).toBe(200);
    expect(ResponseCode.SUCCESS).toBe(200);
  });

  test('should have correct client error codes', () => {
    expect(BAD_REQUEST).toBe(400);
    expect(UNAUTHORIZED).toBe(401);
    expect(FORBIDDEN).toBe(403);
    expect(NOT_FOUND).toBe(404);
    expect(CONFLICT).toBe(409);
    expect(UNPROCESSABLE_ENTITY).toBe(422);
    expect(TOO_MANY_REQUESTS).toBe(429);
  });

  test('should have correct server error codes', () => {
    expect(INTERNAL_SERVER_ERROR).toBe(500);
    expect(BAD_GATEWAY).toBe(502);
    expect(SERVICE_UNAVAILABLE).toBe(503);
    expect(GATEWAY_TIMEOUT).toBe(504);
  });
});

describe('ResponseCode Utility Functions', () => {
  describe('isSuccess', () => {
    test('should return true for success code', () => {
      expect(isSuccess(200)).toBe(true);
      expect(isSuccess(SUCCESS)).toBe(true);
    });

    test('should return false for error codes', () => {
      expect(isSuccess(400)).toBe(false);
      expect(isSuccess(401)).toBe(false);
      expect(isSuccess(500)).toBe(false);
    });
  });

  describe('isClientError', () => {
    test('should return true for client error codes', () => {
      expect(isClientError(400)).toBe(true);
      expect(isClientError(401)).toBe(true);
      expect(isClientError(403)).toBe(true);
      expect(isClientError(404)).toBe(true);
      expect(isClientError(409)).toBe(true);
      expect(isClientError(422)).toBe(true);
      expect(isClientError(429)).toBe(true);
      expect(isClientError(499)).toBe(true); // 边界测试
    });

    test('should return false for non-client error codes', () => {
      expect(isClientError(200)).toBe(false);
      expect(isClientError(399)).toBe(false);
      expect(isClientError(500)).toBe(false);
      expect(isClientError(600)).toBe(false);
    });
  });

  describe('isServerError', () => {
    test('should return true for server error codes', () => {
      expect(isServerError(500)).toBe(true);
      expect(isServerError(502)).toBe(true);
      expect(isServerError(503)).toBe(true);
      expect(isServerError(504)).toBe(true);
      expect(isServerError(599)).toBe(true); // 边界测试
    });

    test('should return false for non-server error codes', () => {
      expect(isServerError(200)).toBe(false);
      expect(isServerError(400)).toBe(false);
      expect(isServerError(499)).toBe(false);
      expect(isServerError(600)).toBe(false);
    });
  });

  describe('isError', () => {
    test('should return false for success code', () => {
      expect(isError(200)).toBe(false);
    });

    test('should return true for error codes', () => {
      expect(isError(400)).toBe(true);
      expect(isError(401)).toBe(true);
      expect(isError(500)).toBe(true);
      expect(isError(503)).toBe(true);
    });
  });

  describe('getDescription', () => {
    test('should return correct descriptions for known codes', () => {
      expect(getDescription(200)).toBe('操作成功');
      expect(getDescription(400)).toBe('请求参数错误');
      expect(getDescription(401)).toBe('未认证，需要登录');
      expect(getDescription(403)).toBe('权限不足');
      expect(getDescription(404)).toBe('资源不存在');
      expect(getDescription(409)).toBe('资源冲突');
      expect(getDescription(422)).toBe('请求语义错误');
      expect(getDescription(429)).toBe('请求频率过高');
      expect(getDescription(500)).toBe('服务器内部错误');
      expect(getDescription(502)).toBe('网关错误');
      expect(getDescription(503)).toBe('服务不可用');
      expect(getDescription(504)).toBe('网关超时');
    });

    test('should return default description for unknown codes', () => {
      expect(getDescription(999)).toBe('未知错误');
      expect(getDescription(123)).toBe('未知错误');
    });
  });
});

describe('ResponseCode Collections', () => {
  test('SUCCESS_CODES should contain only success code', () => {
    expect(SUCCESS_CODES).toEqual([200]);
  });

  test('CLIENT_ERROR_CODES should contain all client error codes', () => {
    expect(CLIENT_ERROR_CODES).toContain(400);
    expect(CLIENT_ERROR_CODES).toContain(401);
    expect(CLIENT_ERROR_CODES).toContain(403);
    expect(CLIENT_ERROR_CODES).toContain(404);
    expect(CLIENT_ERROR_CODES).toContain(409);
    expect(CLIENT_ERROR_CODES).toContain(422);
    expect(CLIENT_ERROR_CODES).toContain(429);
    expect(CLIENT_ERROR_CODES).toHaveLength(7);
  });

  test('SERVER_ERROR_CODES should contain all server error codes', () => {
    expect(SERVER_ERROR_CODES).toContain(500);
    expect(SERVER_ERROR_CODES).toContain(502);
    expect(SERVER_ERROR_CODES).toContain(503);
    expect(SERVER_ERROR_CODES).toContain(504);
    expect(SERVER_ERROR_CODES).toHaveLength(4);
  });

  test('ERROR_CODES should contain all error codes', () => {
    expect(ERROR_CODES).toEqual([...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES]);
    expect(ERROR_CODES).toHaveLength(11);
  });

  test('ALL_CODES should contain all codes', () => {
    expect(ALL_CODES).toEqual([...SUCCESS_CODES, ...ERROR_CODES]);
    expect(ALL_CODES).toHaveLength(12);
  });
});

describe('ResponseCode Object', () => {
  test('should have all constants', () => {
    expect(ResponseCode.SUCCESS).toBe(200);
    expect(ResponseCode.BAD_REQUEST).toBe(400);
    expect(ResponseCode.UNAUTHORIZED).toBe(401);
    expect(ResponseCode.FORBIDDEN).toBe(403);
    expect(ResponseCode.NOT_FOUND).toBe(404);
    expect(ResponseCode.CONFLICT).toBe(409);
    expect(ResponseCode.UNPROCESSABLE_ENTITY).toBe(422);
    expect(ResponseCode.TOO_MANY_REQUESTS).toBe(429);
    expect(ResponseCode.INTERNAL_SERVER_ERROR).toBe(500);
    expect(ResponseCode.BAD_GATEWAY).toBe(502);
    expect(ResponseCode.SERVICE_UNAVAILABLE).toBe(503);
    expect(ResponseCode.GATEWAY_TIMEOUT).toBe(504);
  });

  test('should have utility methods', () => {
    expect(typeof ResponseCode.isSuccess).toBe('function');
    expect(typeof ResponseCode.isClientError).toBe('function');
    expect(typeof ResponseCode.isServerError).toBe('function');
    expect(typeof ResponseCode.isError).toBe('function');
    expect(typeof ResponseCode.getDescription).toBe('function');
  });

  test('should have code collections', () => {
    expect(Array.isArray(ResponseCode.SUCCESS_CODES)).toBe(true);
    expect(Array.isArray(ResponseCode.CLIENT_ERROR_CODES)).toBe(true);
    expect(Array.isArray(ResponseCode.SERVER_ERROR_CODES)).toBe(true);
    expect(Array.isArray(ResponseCode.ERROR_CODES)).toBe(true);
    expect(Array.isArray(ResponseCode.ALL_CODES)).toBe(true);
  });
});
