/**
 * 标准化响应代码常量
 * 
 * 与后端ResponseCode类保持一致，确保前后端响应代码的统一性。
 * 所有API响应处理都应该使用这些预定义的响应代码。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

// ============= 成功状态码 =============

/**
 * 操作成功
 */
export const SUCCESS = 200;

// ============= 客户端错误状态码 =============

/**
 * 请求参数错误或业务逻辑错误
 */
export const BAD_REQUEST = 400;

/**
 * 未认证，需要登录
 */
export const UNAUTHORIZED = 401;

/**
 * 权限不足，已认证但无权限访问
 */
export const FORBIDDEN = 403;

/**
 * 资源不存在
 */
export const NOT_FOUND = 404;

/**
 * 资源冲突（如重复创建、数据冲突等）
 */
export const CONFLICT = 409;

/**
 * 请求格式正确但语义错误（如验证失败）
 */
export const UNPROCESSABLE_ENTITY = 422;

/**
 * 请求频率限制
 */
export const TOO_MANY_REQUESTS = 429;

// ============= 服务器错误状态码 =============

/**
 * 服务器内部错误
 */
export const INTERNAL_SERVER_ERROR = 500;

/**
 * 网关错误
 */
export const BAD_GATEWAY = 502;

/**
 * 服务不可用
 */
export const SERVICE_UNAVAILABLE = 503;

/**
 * 网关超时
 */
export const GATEWAY_TIMEOUT = 504;

// ============= 响应代码分类方法 =============

/**
 * 判断是否为成功响应代码
 * 
 * @param code 响应代码
 * @returns 是否为成功代码
 */
export const isSuccess = (code: number): boolean => {
  return code === SUCCESS;
};

/**
 * 判断是否为客户端错误代码
 * 
 * @param code 响应代码
 * @returns 是否为客户端错误代码
 */
export const isClientError = (code: number): boolean => {
  return code >= 400 && code < 500;
};

/**
 * 判断是否为服务器错误代码
 * 
 * @param code 响应代码
 * @returns 是否为服务器错误代码
 */
export const isServerError = (code: number): boolean => {
  return code >= 500 && code < 600;
};

/**
 * 判断是否为错误代码（非成功代码）
 * 
 * @param code 响应代码
 * @returns 是否为错误代码
 */
export const isError = (code: number): boolean => {
  return !isSuccess(code);
};

// ============= 响应代码描述方法 =============

/**
 * 获取响应代码的默认描述
 * 
 * @param code 响应代码
 * @returns 响应代码描述
 */
export const getDescription = (code: number): string => {
  switch (code) {
    case SUCCESS:
      return '操作成功';
    case BAD_REQUEST:
      return '请求参数错误';
    case UNAUTHORIZED:
      return '未认证，需要登录';
    case FORBIDDEN:
      return '权限不足';
    case NOT_FOUND:
      return '资源不存在';
    case CONFLICT:
      return '资源冲突';
    case UNPROCESSABLE_ENTITY:
      return '请求语义错误';
    case TOO_MANY_REQUESTS:
      return '请求频率过高';
    case INTERNAL_SERVER_ERROR:
      return '服务器内部错误';
    case BAD_GATEWAY:
      return '网关错误';
    case SERVICE_UNAVAILABLE:
      return '服务不可用';
    case GATEWAY_TIMEOUT:
      return '网关超时';
    default:
      return '未知错误';
  }
};

// ============= 响应代码集合 =============

/**
 * 所有成功响应代码
 */
export const SUCCESS_CODES = [SUCCESS];

/**
 * 所有客户端错误响应代码
 */
export const CLIENT_ERROR_CODES = [
  BAD_REQUEST,
  UNAUTHORIZED,
  FORBIDDEN,
  NOT_FOUND,
  CONFLICT,
  UNPROCESSABLE_ENTITY,
  TOO_MANY_REQUESTS,
];

/**
 * 所有服务器错误响应代码
 */
export const SERVER_ERROR_CODES = [
  INTERNAL_SERVER_ERROR,
  BAD_GATEWAY,
  SERVICE_UNAVAILABLE,
  GATEWAY_TIMEOUT,
];

/**
 * 所有错误响应代码
 */
export const ERROR_CODES = [...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES];

/**
 * 所有响应代码
 */
export const ALL_CODES = [...SUCCESS_CODES, ...ERROR_CODES];

// ============= 默认导出 =============

/**
 * 响应代码常量对象
 */
export const ResponseCode = {
  // 成功状态码
  SUCCESS,
  
  // 客户端错误状态码
  BAD_REQUEST,
  UNAUTHORIZED,
  FORBIDDEN,
  NOT_FOUND,
  CONFLICT,
  UNPROCESSABLE_ENTITY,
  TOO_MANY_REQUESTS,
  
  // 服务器错误状态码
  INTERNAL_SERVER_ERROR,
  BAD_GATEWAY,
  SERVICE_UNAVAILABLE,
  GATEWAY_TIMEOUT,
  
  // 工具方法
  isSuccess,
  isClientError,
  isServerError,
  isError,
  getDescription,
  
  // 代码集合
  SUCCESS_CODES,
  CLIENT_ERROR_CODES,
  SERVER_ERROR_CODES,
  ERROR_CODES,
  ALL_CODES,
} as const;

export default ResponseCode;
