/**
 * 错误处理演示页面
 * 
 * 用于测试标准化错误处理机制的功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import React from 'react';
import { Card, Button, Space, Typography, Divider } from 'antd';
import { ResponseCode } from '@/constants/responseCodes';
import { handleApiError, showSuccess, showWarning, showError, showInfo, showNotification } from '@/utils/errorHandler';
import type { ApiResponse } from '@/types/api';

const { Title, Paragraph, Text } = Typography;

const ErrorHandlingDemo: React.FC = () => {
  // 模拟API响应
  const simulateApiResponse = (code: number, message: string): ApiResponse<any> => ({
    code,
    message,
    data: null,
    timestamp: new Date().toISOString(),
  });

  // 测试不同的错误代码
  const testErrorCode = (code: number, message: string) => {
    const response = simulateApiResponse(code, message);
    handleApiError(response);
  };

  // 测试成功响应
  const testSuccessResponse = () => {
    const response = simulateApiResponse(ResponseCode.SUCCESS, '操作成功');
    handleApiError(response); // 应该不显示任何消息
    showSuccess('这是一个成功消息');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>标准化错误处理演示</Title>
        <Paragraph>
          这个页面用于演示和测试我们的标准化错误处理机制。
          点击下面的按钮来模拟不同的API响应代码，观察错误消息的显示效果。
        </Paragraph>

        <Divider orientation="left">成功响应测试</Divider>
        <Space wrap>
          <Button type="primary" onClick={testSuccessResponse}>
            测试成功响应 (200)
          </Button>
        </Space>

        <Divider orientation="left">客户端错误测试</Divider>
        <Space wrap>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.BAD_REQUEST, '请求参数错误，用户名不能为空')}
          >
            参数错误 (400)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.UNAUTHORIZED, '登录已过期，请重新登录')}
          >
            未认证 (401)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.FORBIDDEN, '您没有权限访问该资源')}
          >
            权限不足 (403)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.NOT_FOUND, '用户不存在')}
          >
            资源不存在 (404)
          </Button>
          <Button 
            onClick={() => testErrorCode(ResponseCode.CONFLICT, '邮箱已被注册')}
          >
            资源冲突 (409)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.UNPROCESSABLE_ENTITY, '验证码错误')}
          >
            语义错误 (422)
          </Button>
          <Button 
            onClick={() => testErrorCode(ResponseCode.TOO_MANY_REQUESTS, '请求频率过高，请60秒后重试')}
          >
            频率限制 (429)
          </Button>
        </Space>

        <Divider orientation="left">服务器错误测试</Divider>
        <Space wrap>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.INTERNAL_SERVER_ERROR, '数据库连接失败')}
          >
            内部错误 (500)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.BAD_GATEWAY, '网关配置错误')}
          >
            网关错误 (502)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.SERVICE_UNAVAILABLE, '系统维护中')}
          >
            服务不可用 (503)
          </Button>
          <Button 
            danger 
            onClick={() => testErrorCode(ResponseCode.GATEWAY_TIMEOUT, '上游服务超时')}
          >
            网关超时 (504)
          </Button>
        </Space>

        <Divider orientation="left">消息组件测试</Divider>
        <Space wrap>
          <Button type="primary" onClick={() => showSuccess('这是成功消息')}>
            成功消息
          </Button>
          <Button onClick={() => showInfo('这是信息消息')}>
            信息消息
          </Button>
          <Button onClick={() => showWarning('这是警告消息')}>
            警告消息
          </Button>
          <Button danger onClick={() => showError('这是错误消息')}>
            错误消息
          </Button>
          <Button 
            type="dashed" 
            onClick={() => showNotification('通知标题', '这是一个通知消息的详细描述', 'info')}
          >
            通知消息
          </Button>
        </Space>

        <Divider orientation="left">响应代码信息</Divider>
        <div style={{ background: '#f5f5f5', padding: '16px', borderRadius: '6px' }}>
          <Title level={4}>当前支持的响应代码：</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '8px' }}>
            <Text><Text code>200</Text> 操作成功</Text>
            <Text><Text code>400</Text> 请求参数错误</Text>
            <Text><Text code>401</Text> 未认证，需要登录</Text>
            <Text><Text code>403</Text> 权限不足</Text>
            <Text><Text code>404</Text> 资源不存在</Text>
            <Text><Text code>409</Text> 资源冲突</Text>
            <Text><Text code>422</Text> 请求语义错误</Text>
            <Text><Text code>429</Text> 请求频率过高</Text>
            <Text><Text code>500</Text> 服务器内部错误</Text>
            <Text><Text code>502</Text> 网关错误</Text>
            <Text><Text code>503</Text> 服务不可用</Text>
            <Text><Text code>504</Text> 网关超时</Text>
          </div>
        </div>

        <Divider orientation="left">说明</Divider>
        <Paragraph>
          <ul>
            <li><Text strong>401 未认证错误</Text>：会自动跳转到登录页面并清除本地认证信息</li>
            <li><Text strong>409 冲突</Text> 和 <Text strong>429 频率限制</Text>：使用警告样式显示</li>
            <li><Text strong>500 系列服务器错误</Text>：使用预设的用户友好消息</li>
            <li><Text strong>其他错误</Text>：使用错误样式显示后端返回的具体消息</li>
          </ul>
        </Paragraph>
      </Card>
    </div>
  );
};

export default ErrorHandlingDemo;
