/**
 * API Response Handler Utilities
 * 
 * Provides standardized utilities for handling API responses consistently
 * across the application. These utilities ensure that response.code is always
 * checked and errors are handled appropriately using Ant Design's message component.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import { message } from 'antd';
import { ResponseCode } from '@/constants/responseCodes';
import { handleApiError, ErrorDisplayType } from '@/utils/errorHandler';
import type { ApiResponse } from '@/types/api';

// ============= Response Validation Utilities =============

/**
 * Check if an API response is successful
 * 
 * @param response API response object
 * @returns true if response code indicates success
 */
export const isSuccessResponse = <T>(response: ApiResponse<T>): boolean => {
  return ResponseCode.isSuccess(response.code);
};

/**
 * Validate API response and throw error if not successful
 * 
 * @param response API response object
 * @param customErrorMessage Optional custom error message
 * @throws Error if response code indicates failure
 */
export const validateApiResponse = <T>(
  response: ApiResponse<T>,
  customErrorMessage?: string
): void => {
  if (!isSuccessResponse(response)) {
    // Use centralized error handler to display message
    handleApiError(response, {
      customMessage: customErrorMessage,
    });
    
    // Throw error for caller to handle
    throw new Error(customErrorMessage || response.message);
  }
};

// ============= Response Handler Functions =============

/**
 * Handle API response with automatic error checking and message display
 * 
 * @param response API response object
 * @param options Configuration options
 * @returns response data if successful, null if failed
 */
export const handleApiResponse = <T>(
  response: ApiResponse<T>,
  options: {
    /** Custom error message to display */
    customErrorMessage?: string;
    /** Whether to show success message */
    showSuccessMessage?: boolean;
    /** Custom success message */
    successMessage?: string;
    /** Error display type override */
    errorDisplayType?: ErrorDisplayType;
    /** Whether to throw error on failure */
    throwOnError?: boolean;
  } = {}
): T | null => {
  const {
    customErrorMessage,
    showSuccessMessage = false,
    successMessage,
    errorDisplayType,
    throwOnError = false,
  } = options;

  if (isSuccessResponse(response)) {
    // Show success message if requested
    if (showSuccessMessage) {
      message.success(successMessage || '操作成功');
    }
    
    return response.data;
  } else {
    // Handle error response
    handleApiError(response, {
      customMessage: customErrorMessage,
      displayType: errorDisplayType,
    });
    
    if (throwOnError) {
      throw new Error(customErrorMessage || response.message);
    }
    
    return null;
  }
};

/**
 * Handle API response for operations that don't return data (void responses)
 * 
 * @param response API response object
 * @param options Configuration options
 * @returns true if successful, false if failed
 */
export const handleVoidApiResponse = (
  response: ApiResponse<void>,
  options: {
    /** Custom error message to display */
    customErrorMessage?: string;
    /** Whether to show success message */
    showSuccessMessage?: boolean;
    /** Custom success message */
    successMessage?: string;
    /** Error display type override */
    errorDisplayType?: ErrorDisplayType;
    /** Whether to throw error on failure */
    throwOnError?: boolean;
  } = {}
): boolean => {
  const {
    customErrorMessage,
    showSuccessMessage = false,
    successMessage,
    errorDisplayType,
    throwOnError = false,
  } = options;

  if (isSuccessResponse(response)) {
    // Show success message if requested
    if (showSuccessMessage) {
      message.success(successMessage || '操作成功');
    }
    
    return true;
  } else {
    // Handle error response
    handleApiError(response, {
      customMessage: customErrorMessage,
      displayType: errorDisplayType,
    });
    
    if (throwOnError) {
      throw new Error(customErrorMessage || response.message);
    }
    
    return false;
  }
};

// ============= Wrapper Functions for Common Patterns =============

/**
 * Wrapper for API calls that should show success messages
 * 
 * @param apiCall Function that returns an API response
 * @param successMessage Message to show on success
 * @param errorMessage Message to show on error
 * @returns Promise that resolves to response data or null
 */
export const withSuccessMessage = async <T>(
  apiCall: () => Promise<ApiResponse<T>>,
  successMessage: string,
  errorMessage?: string
): Promise<T | null> => {
  try {
    const response = await apiCall();
    return handleApiResponse(response, {
      showSuccessMessage: true,
      successMessage,
      customErrorMessage: errorMessage,
    });
  } catch (error) {
    console.error('API call failed:', error);
    if (errorMessage) {
      message.error(errorMessage);
    }
    return null;
  }
};

/**
 * Wrapper for API calls that should throw errors on failure
 * 
 * @param apiCall Function that returns an API response
 * @param errorMessage Custom error message
 * @returns Promise that resolves to response data
 * @throws Error if API call fails
 */
export const withErrorThrow = async <T>(
  apiCall: () => Promise<ApiResponse<T>>,
  errorMessage?: string
): Promise<T> => {
  const response = await apiCall();
  return handleApiResponse(response, {
    customErrorMessage: errorMessage,
    throwOnError: true,
  })!;
};

/**
 * Wrapper for void API calls (operations that don't return data)
 * 
 * @param apiCall Function that returns a void API response
 * @param successMessage Message to show on success
 * @param errorMessage Message to show on error
 * @returns Promise that resolves to boolean indicating success
 */
export const withVoidResponse = async (
  apiCall: () => Promise<ApiResponse<void>>,
  successMessage?: string,
  errorMessage?: string
): Promise<boolean> => {
  try {
    const response = await apiCall();
    return handleVoidApiResponse(response, {
      showSuccessMessage: !!successMessage,
      successMessage,
      customErrorMessage: errorMessage,
    });
  } catch (error) {
    console.error('API call failed:', error);
    if (errorMessage) {
      message.error(errorMessage);
    }
    return false;
  }
};

// ============= Convenience Functions =============

/**
 * Quick success check for API responses
 * 
 * @param response API response object
 * @returns true if successful, false otherwise
 */
export const isSuccess = <T>(response: ApiResponse<T>): boolean => {
  return isSuccessResponse(response);
};

/**
 * Quick error handling for API responses
 * 
 * @param response API response object
 * @param errorMessage Custom error message
 */
export const handleError = <T>(
  response: ApiResponse<T>,
  errorMessage?: string
): void => {
  if (!isSuccessResponse(response)) {
    handleApiError(response, {
      customMessage: errorMessage,
    });
  }
};

/**
 * Extract data from successful API response, null if failed
 * 
 * @param response API response object
 * @returns response data or null
 */
export const extractData = <T>(response: ApiResponse<T>): T | null => {
  return isSuccessResponse(response) ? response.data : null;
};

/**
 * Extract data from successful API response, throw if failed
 * 
 * @param response API response object
 * @param errorMessage Custom error message
 * @returns response data
 * @throws Error if response indicates failure
 */
export const extractDataOrThrow = <T>(
  response: ApiResponse<T>,
  errorMessage?: string
): T => {
  if (isSuccessResponse(response)) {
    return response.data;
  } else {
    handleApiError(response, {
      customMessage: errorMessage,
    });
    throw new Error(errorMessage || response.message);
  }
};
