package com.teammanage.common;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 统一API响应格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

public class ApiResponse<T> {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }

    public ApiResponse(Integer code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS, "success", data);
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(ResponseCode.SUCCESS, "success", null);
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS, message, data);
    }

    /**
     * 错误响应
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    /**
     * 错误响应（默认500）
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(ResponseCode.INTERNAL_SERVER_ERROR, message, null);
    }

    /**
     * 参数错误响应
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(ResponseCode.BAD_REQUEST, message, null);
    }

    /**
     * 未认证响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return new ApiResponse<>(ResponseCode.UNAUTHORIZED, message, null);
    }

    /**
     * 权限不足响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return new ApiResponse<>(ResponseCode.FORBIDDEN, message, null);
    }

    /**
     * 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(ResponseCode.NOT_FOUND, message, null);
    }

    /**
     * 资源冲突响应
     */
    public static <T> ApiResponse<T> conflict(String message) {
        return new ApiResponse<>(ResponseCode.CONFLICT, message, null);
    }

    /**
     * 请求语义错误响应
     */
    public static <T> ApiResponse<T> unprocessableEntity(String message) {
        return new ApiResponse<>(ResponseCode.UNPROCESSABLE_ENTITY, message, null);
    }

    /**
     * 请求频率限制响应
     */
    public static <T> ApiResponse<T> tooManyRequests(String message) {
        return new ApiResponse<>(ResponseCode.TOO_MANY_REQUESTS, message, null);
    }

    /**
     * 服务不可用响应
     */
    public static <T> ApiResponse<T> serviceUnavailable(String message) {
        return new ApiResponse<>(ResponseCode.SERVICE_UNAVAILABLE, message, null);
    }

    // Getter and Setter methods
    public Integer getCode() { return code; }
    public void setCode(Integer code) { this.code = code; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public T getData() { return data; }
    public void setData(T data) { this.data = data; }

    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

}
