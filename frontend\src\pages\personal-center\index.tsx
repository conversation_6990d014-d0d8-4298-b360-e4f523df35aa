import { useModel, history } from '@umijs/max';
import { Card, Col, Row, Spin, Alert } from 'antd';
import React, { useEffect, useState } from 'react';
import UserFloatButton from '@/components/FloatButton';
import TeamListCard from './TeamListCard';
import TodoManagement from './TodoManagement';
import UserProfileCard from './UserProfileCard';

const PersonalCenterPage: React.FC = () => {
  const { initialState, loading } = useModel('@@initialState');
  const [renderError, setRenderError] = useState<string | null>(null);

  // 调试信息
  console.log('PersonalCenterPage 渲染:', {
    loading,
    currentUser: initialState?.currentUser,
    currentTeam: initialState?.currentTeam,
  });

  // 错误边界处理
  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('页面渲染错误:', error);
      setRenderError('页面加载出现错误，请刷新页面重试');
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Spin size="large" />
        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>
      </div>
    );
  }

  // 如果用户未登录，跳转到登录页
  useEffect(() => {
    if (!loading && !initialState?.currentUser) {
      history.push('/user/login');
    }
  }, [loading, initialState?.currentUser]);

  // 如果有渲染错误，显示错误信息
  if (renderError) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '20px',
        }}
      >
        <Alert
          message="页面加载失败"
          description={renderError}
          type="error"
          showIcon
          action={
            <button
              onClick={() => window.location.reload()}
              style={{
                background: '#ff4d4f',
                color: 'white',
                border: 'none',
                padding: '4px 12px',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              刷新页面
            </button>
          }
        />
      </div>
    );
  }

  // 如果用户未登录，不渲染页面内容（避免闪烁）
  if (!loading && !initialState?.currentUser) {
    return null;
  }

  // 如果用户已登录但还在加载中，显示加载状态
  if (initialState?.currentUser && loading) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Spin size="large" />
        <div style={{ marginLeft: 16 }}>正在加载个人中心...</div>
      </div>
    );
  }

  // 确保有用户信息才渲染页面内容
  if (!initialState?.currentUser) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Spin size="large" />
        <div style={{ marginLeft: 16 }}>正在验证登录状态...</div>
      </div>
    );
  }

  return (
    <>
      <div
        style={{
          minHeight: '100vh',
          background: '#f5f8ff',
          padding: '12px 12px 24px 12px', // 移动端减少左右边距
        }}
      >
        {/* 大的容器区域 */}
        <Card
          style={{
            width: '100%',
            minHeight: 'calc(100vh - 48px)',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          }}
          styles={{
            body: {
              padding: '24px',
            },
          }}
        >
        <Row gutter={[16, 16]} style={{ margin: 0 }}>
          {/* 个人信息卡片 - 全宽显示 */}
          <Col xs={24} style={{ marginBottom: 8 }}>
            {(() => {
              try {
                return <UserProfileCard />;
              } catch (error) {
                console.error('UserProfileCard 渲染错误:', error);
                return (
                  <Alert
                    message="个人信息加载失败"
                    description="个人信息组件出现错误，请刷新页面重试"
                    type="error"
                    showIcon
                  />
                );
              }
            })()}
          </Col>

          {/* 待办事项 - 响应式布局 */}
          <Col
            xs={24}
            sm={24}
            md={24}
            lg={12}
            xl={12}
            xxl={12}
            style={{ marginBottom: 8 }}
          >
            {(() => {
              try {
                return <TodoManagement />;
              } catch (error) {
                console.error('TodoManagement 渲染错误:', error);
                return (
                  <Alert
                    message="待办事项加载失败"
                    description="待办事项组件出现错误，请刷新页面重试"
                    type="error"
                    showIcon
                  />
                );
              }
            })()}
          </Col>

          {/* 团队列表 - 响应式布局 */}
          <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
            {(() => {
              try {
                return <TeamListCard />;
              } catch (error) {
                console.error('TeamListCard 渲染错误:', error);
                return (
                  <Alert
                    message="团队列表加载失败"
                    description="团队列表组件出现错误，请刷新页面重试"
                    type="error"
                    showIcon
                  />
                );
              }
            })()}
          </Col>
        </Row>
      </Card>
    </div>

    {/* 浮动按钮 */}
    <UserFloatButton />
  </>
  );
};

export default PersonalCenterPage;
